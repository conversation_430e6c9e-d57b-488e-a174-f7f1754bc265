import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/payment_model.dart';

/// Repository for managing payments in Firebase
/// 
/// This repository handles:
/// - Payment CRUD operations
/// - Payment history and tracking
/// - Outstanding dues management
/// - Receipt generation
/// - Firebase data synchronization
/// - Integration with existing payment system
class PaymentRepository {
  /// Firebase Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Collection reference for payments
  static const String _collectionName = 'payments';
  
  /// Get collection reference
  CollectionReference get _collection => _firestore.collection(_collectionName);

  /// Create a new payment
  /// 
  /// Takes a [Payment] object and creates it in Firebase.
  /// Returns the created payment with the assigned ID.
  /// Throws an exception if the operation fails.
  Future<Payment> createPayment(Payment payment) async {
    try {
      print('🎯 PaymentRepository: Creating payment: ${payment.receiptNumber}');
      
      // Create payment document
      final docRef = await _collection.add(payment.toJson());
      
      // Return payment with assigned ID
      final createdPayment = payment.copyWith(id: docRef.id);
      
      print('✅ PaymentRepository: Created payment with ID: ${docRef.id}');
      return createdPayment;
      
    } catch (e) {
      print('❌ PaymentRepository: Error creating payment: $e');
      throw Exception('Failed to create payment: $e');
    }
  }

  /// Get all payments
  /// 
  /// Retrieves all payments from Firebase.
  /// Returns a list of [Payment] objects.
  /// Throws an exception if the operation fails.
  Future<List<Payment>> getAllPayments() async {
    try {
      print('🔍 PaymentRepository: Fetching all payments');
      
      final querySnapshot = await _collection
          .orderBy('createdAt', descending: true)
          .get();
      
      final payments = querySnapshot.docs
          .map((doc) => Payment.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ PaymentRepository: Retrieved ${payments.length} payments');
      return payments;
      
    } catch (e) {
      print('❌ PaymentRepository: Error fetching payments: $e');
      throw Exception('Failed to fetch payments: $e');
    }
  }

  /// Get payment by ID
  /// 
  /// Retrieves a specific payment by its ID.
  /// Returns a [Payment] object or null if not found.
  /// Throws an exception if the operation fails.
  Future<Payment?> getPaymentById(String id) async {
    try {
      print('🔍 PaymentRepository: Fetching payment with ID: $id');
      
      final docSnapshot = await _collection.doc(id).get();
      
      if (!docSnapshot.exists) {
        print('⚠️ PaymentRepository: Payment not found with ID: $id');
        return null;
      }
      
      final payment = Payment.fromJson(
        docSnapshot.data() as Map<String, dynamic>,
        docSnapshot.id,
      );
      
      print('✅ PaymentRepository: Retrieved payment: ${payment.receiptNumber}');
      return payment;
      
    } catch (e) {
      print('❌ PaymentRepository: Error fetching payment: $e');
      throw Exception('Failed to fetch payment: $e');
    }
  }

  /// Update an existing payment
  /// 
  /// Takes a [Payment] object and updates it in Firebase.
  /// Returns the updated payment.
  /// Throws an exception if the operation fails.
  Future<Payment> updatePayment(Payment payment) async {
    try {
      print('🎯 PaymentRepository: Updating payment: ${payment.receiptNumber}');
      
      final updatedPayment = payment.copyWith(updatedAt: DateTime.now());
      
      await _collection.doc(payment.id).update(updatedPayment.toJson());
      
      print('✅ PaymentRepository: Updated payment: ${payment.receiptNumber}');
      return updatedPayment;
      
    } catch (e) {
      print('❌ PaymentRepository: Error updating payment: $e');
      throw Exception('Failed to update payment: $e');
    }
  }

  /// Delete a payment
  /// 
  /// Deletes a payment from Firebase by its ID.
  /// Throws an exception if the operation fails.
  Future<void> deletePayment(String id) async {
    try {
      print('🎯 PaymentRepository: Deleting payment with ID: $id');
      
      await _collection.doc(id).delete();
      
      print('✅ PaymentRepository: Deleted payment with ID: $id');
      
    } catch (e) {
      print('❌ PaymentRepository: Error deleting payment: $e');
      throw Exception('Failed to delete payment: $e');
    }
  }

  /// Get payments by student ID
  /// 
  /// Retrieves all payments for a specific student.
  /// Returns a list of [Payment] objects.
  /// Throws an exception if the operation fails.
  Future<List<Payment>> getPaymentsByStudentId(String studentId) async {
    try {
      print('🔍 PaymentRepository: Fetching payments for student: $studentId');
      
      final querySnapshot = await _collection
          .where('studentId', isEqualTo: studentId)
          .orderBy('createdAt', descending: true)
          .get();
      
      final payments = querySnapshot.docs
          .map((doc) => Payment.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ PaymentRepository: Retrieved ${payments.length} payments for student $studentId');
      return payments;
      
    } catch (e) {
      print('❌ PaymentRepository: Error fetching payments by student: $e');
      throw Exception('Failed to fetch payments by student: $e');
    }
  }

  /// Get payments by status
  /// 
  /// Retrieves payments with a specific status.
  /// Returns a list of [Payment] objects.
  /// Throws an exception if the operation fails.
  Future<List<Payment>> getPaymentsByStatus(PaymentStatus status) async {
    try {
      print('🔍 PaymentRepository: Fetching payments with status: ${status.name}');
      
      final querySnapshot = await _collection
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true)
          .get();
      
      final payments = querySnapshot.docs
          .map((doc) => Payment.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ PaymentRepository: Retrieved ${payments.length} payments with status ${status.name}');
      return payments;
      
    } catch (e) {
      print('❌ PaymentRepository: Error fetching payments by status: $e');
      throw Exception('Failed to fetch payments by status: $e');
    }
  }

  /// Get payments by date range
  /// 
  /// Retrieves payments within a specific date range.
  /// Returns a list of [Payment] objects.
  /// Throws an exception if the operation fails.
  Future<List<Payment>> getPaymentsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      print('🔍 PaymentRepository: Fetching payments from $startDate to $endDate');
      
      final querySnapshot = await _collection
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('createdAt', descending: true)
          .get();
      
      final payments = querySnapshot.docs
          .map((doc) => Payment.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ PaymentRepository: Retrieved ${payments.length} payments in date range');
      return payments;
      
    } catch (e) {
      print('❌ PaymentRepository: Error fetching payments by date range: $e');
      throw Exception('Failed to fetch payments by date range: $e');
    }
  }

  /// Get outstanding payments
  /// 
  /// Retrieves all pending and overdue payments.
  /// Returns a list of [Payment] objects.
  /// Throws an exception if the operation fails.
  Future<List<Payment>> getOutstandingPayments() async {
    try {
      print('🔍 PaymentRepository: Fetching outstanding payments');
      
      final querySnapshot = await _collection
          .where('status', whereIn: [PaymentStatus.pending.name, PaymentStatus.overdue.name])
          .orderBy('dueDate')
          .get();
      
      final payments = querySnapshot.docs
          .map((doc) => Payment.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ PaymentRepository: Retrieved ${payments.length} outstanding payments');
      return payments;
      
    } catch (e) {
      print('❌ PaymentRepository: Error fetching outstanding payments: $e');
      throw Exception('Failed to fetch outstanding payments: $e');
    }
  }

  /// Get payment statistics
  /// 
  /// Retrieves payment statistics for dashboard.
  /// Returns a map with various payment metrics.
  /// Throws an exception if the operation fails.
  Future<Map<String, dynamic>> getPaymentStatistics() async {
    try {
      print('🔍 PaymentRepository: Calculating payment statistics');
      
      final allPayments = await getAllPayments();
      
      final totalRevenue = allPayments
          .where((payment) => payment.status == PaymentStatus.completed)
          .fold(0.0, (sum, payment) => sum + payment.totalAmount);
      
      final pendingAmount = allPayments
          .where((payment) => payment.status == PaymentStatus.pending)
          .fold(0.0, (sum, payment) => sum + payment.totalAmount);
      
      final overdueAmount = allPayments
          .where((payment) => payment.status == PaymentStatus.overdue)
          .fold(0.0, (sum, payment) => sum + payment.totalAmount);
      
      final thisMonthRevenue = allPayments
          .where((payment) => 
              payment.status == PaymentStatus.completed &&
              payment.paidAt != null &&
              payment.paidAt!.month == DateTime.now().month &&
              payment.paidAt!.year == DateTime.now().year)
          .fold(0.0, (sum, payment) => sum + payment.totalAmount);
      
      final statistics = {
        'totalRevenue': totalRevenue,
        'pendingAmount': pendingAmount,
        'overdueAmount': overdueAmount,
        'thisMonthRevenue': thisMonthRevenue,
        'totalPayments': allPayments.length,
        'completedPayments': allPayments.where((p) => p.status == PaymentStatus.completed).length,
        'pendingPayments': allPayments.where((p) => p.status == PaymentStatus.pending).length,
        'overduePayments': allPayments.where((p) => p.status == PaymentStatus.overdue).length,
      };
      
      print('✅ PaymentRepository: Calculated payment statistics');
      return statistics;
      
    } catch (e) {
      print('❌ PaymentRepository: Error calculating payment statistics: $e');
      throw Exception('Failed to calculate payment statistics: $e');
    }
  }

  /// Search payments
  /// 
  /// Searches for payments by student name, receipt number, or transaction ID.
  /// Returns a list of [Payment] objects.
  /// Throws an exception if the operation fails.
  Future<List<Payment>> searchPayments(String searchTerm) async {
    try {
      print('🔍 PaymentRepository: Searching payments with term: $searchTerm');
      
      // Search by student name
      final nameQuery = await _collection
          .where('studentName', isGreaterThanOrEqualTo: searchTerm)
          .where('studentName', isLessThan: searchTerm + 'z')
          .get();
      
      // Search by receipt number
      final receiptQuery = await _collection
          .where('receiptNumber', isGreaterThanOrEqualTo: searchTerm)
          .where('receiptNumber', isLessThan: searchTerm + 'z')
          .get();
      
      final payments = <Payment>[];
      final addedIds = <String>{};
      
      // Add results from name search
      for (final doc in nameQuery.docs) {
        if (!addedIds.contains(doc.id)) {
          payments.add(Payment.fromJson(
            doc.data() as Map<String, dynamic>,
            doc.id,
          ));
          addedIds.add(doc.id);
        }
      }
      
      // Add results from receipt search
      for (final doc in receiptQuery.docs) {
        if (!addedIds.contains(doc.id)) {
          payments.add(Payment.fromJson(
            doc.data() as Map<String, dynamic>,
            doc.id,
          ));
          addedIds.add(doc.id);
        }
      }
      
      // Sort by creation date
      payments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      print('✅ PaymentRepository: Found ${payments.length} payments matching "$searchTerm"');
      return payments;
      
    } catch (e) {
      print('❌ PaymentRepository: Error searching payments: $e');
      throw Exception('Failed to search payments: $e');
    }
  }

  /// Update payment status
  /// 
  /// Updates the status of a payment.
  /// Returns the updated payment.
  /// Throws an exception if the operation fails.
  Future<Payment> updatePaymentStatus(String id, PaymentStatus status) async {
    try {
      print('🎯 PaymentRepository: Updating payment status: $id to ${status.name}');
      
      final payment = await getPaymentById(id);
      if (payment == null) {
        throw Exception('Payment not found');
      }
      
      final updatedPayment = payment.copyWith(
        status: status,
        paidAt: status == PaymentStatus.completed ? DateTime.now() : payment.paidAt,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedPayment.toJson());
      
      print('✅ PaymentRepository: Updated payment status: $id');
      return updatedPayment;
      
    } catch (e) {
      print('❌ PaymentRepository: Error updating payment status: $e');
      throw Exception('Failed to update payment status: $e');
    }
  }
}
