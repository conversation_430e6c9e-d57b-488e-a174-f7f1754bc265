import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/exam_model.dart';

/// Repository for managing exams in Firebase
/// 
/// This repository handles:
/// - Exam CRUD operations
/// - Exam scheduling and management
/// - Question management
/// - Result processing
/// - Firebase data synchronization
/// - Integration with course and student systems
class ExamRepository {
  /// Firebase Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Collection reference for exams
  static const String _collectionName = 'exams';
  
  /// Get collection reference
  CollectionReference get _collection => _firestore.collection(_collectionName);

  /// Create a new exam
  Future<Exam> createExam(Exam exam) async {
    try {
      print('🎯 ExamRepository: Creating exam: ${exam.title}');
      final docRef = await _collection.add(exam.toJson());
      final createdExam = exam.copyWith(id: docRef.id);
      print('✅ ExamRepository: Created exam with ID: ${docRef.id}');
      return createdExam;
    } catch (e) {
      print('❌ ExamRepository: Error creating exam: $e');
      throw Exception('Failed to create exam: $e');
    }
  }

  /// Get all exams
  Future<List<Exam>> getAllExams() async {
    try {
      print('🔍 ExamRepository: Fetching all exams');
      final querySnapshot = await _collection
          .orderBy('startDateTime', descending: true)
          .get();
      
      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ ExamRepository: Retrieved ${exams.length} exams');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exams: $e');
      throw Exception('Failed to fetch exams: $e');
    }
  }

  /// Get exam by ID
  Future<Exam?> getExamById(String id) async {
    try {
      print('🔍 ExamRepository: Fetching exam with ID: $id');
      final docSnapshot = await _collection.doc(id).get();
      
      if (!docSnapshot.exists) {
        print('⚠️ ExamRepository: Exam not found with ID: $id');
        return null;
      }
      
      final exam = Exam.fromJson(
        docSnapshot.data() as Map<String, dynamic>,
        docSnapshot.id,
      );
      
      print('✅ ExamRepository: Retrieved exam: ${exam.title}');
      return exam;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exam: $e');
      throw Exception('Failed to fetch exam: $e');
    }
  }

  /// Update an existing exam
  Future<Exam> updateExam(Exam exam) async {
    try {
      print('🎯 ExamRepository: Updating exam: ${exam.title}');
      final updatedExam = exam.copyWith(updatedAt: DateTime.now());
      await _collection.doc(exam.id).update(updatedExam.toJson());
      print('✅ ExamRepository: Updated exam: ${exam.title}');
      return updatedExam;
    } catch (e) {
      print('❌ ExamRepository: Error updating exam: $e');
      throw Exception('Failed to update exam: $e');
    }
  }

  /// Delete an exam
  Future<void> deleteExam(String id) async {
    try {
      print('🎯 ExamRepository: Deleting exam with ID: $id');
      await _collection.doc(id).delete();
      print('✅ ExamRepository: Deleted exam with ID: $id');
    } catch (e) {
      print('❌ ExamRepository: Error deleting exam: $e');
      throw Exception('Failed to delete exam: $e');
    }
  }

  /// Get exams by course ID
  Future<List<Exam>> getExamsByCourseId(String courseId) async {
    try {
      print('🔍 ExamRepository: Fetching exams for course: $courseId');
      final querySnapshot = await _collection
          .where('courseId', isEqualTo: courseId)
          .orderBy('startDateTime', descending: true)
          .get();
      
      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ ExamRepository: Retrieved ${exams.length} exams for course $courseId');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exams by course: $e');
      throw Exception('Failed to fetch exams by course: $e');
    }
  }

  /// Get upcoming exams
  Future<List<Exam>> getUpcomingExams() async {
    try {
      print('🔍 ExamRepository: Fetching upcoming exams');
      final now = DateTime.now();
      final querySnapshot = await _collection
          .where('isPublished', isEqualTo: true)
          .where('startDateTime', isGreaterThan: Timestamp.fromDate(now))
          .orderBy('startDateTime')
          .get();
      
      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ ExamRepository: Retrieved ${exams.length} upcoming exams');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching upcoming exams: $e');
      throw Exception('Failed to fetch upcoming exams: $e');
    }
  }

  /// Get active exams
  Future<List<Exam>> getActiveExams() async {
    try {
      print('🔍 ExamRepository: Fetching active exams');
      final now = DateTime.now();
      final querySnapshot = await _collection
          .where('isPublished', isEqualTo: true)
          .where('startDateTime', isLessThanOrEqualTo: Timestamp.fromDate(now))
          .where('endDateTime', isGreaterThan: Timestamp.fromDate(now))
          .orderBy('startDateTime')
          .get();
      
      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ ExamRepository: Retrieved ${exams.length} active exams');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching active exams: $e');
      throw Exception('Failed to fetch active exams: $e');
    }
  }

  /// Search exams
  Future<List<Exam>> searchExams(String searchTerm) async {
    try {
      print('🔍 ExamRepository: Searching exams with term: $searchTerm');
      
      // Search by title
      final titleQuery = await _collection
          .where('title', isGreaterThanOrEqualTo: searchTerm)
          .where('title', isLessThan: searchTerm + 'z')
          .get();
      
      final exams = titleQuery.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      // Sort by start date
      exams.sort((a, b) => b.startDateTime.compareTo(a.startDateTime));
      
      print('✅ ExamRepository: Found ${exams.length} exams matching "$searchTerm"');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error searching exams: $e');
      throw Exception('Failed to search exams: $e');
    }
  }

  /// Publish exam
  Future<Exam> publishExam(String id) async {
    try {
      print('🎯 ExamRepository: Publishing exam: $id');
      final exam = await getExamById(id);
      if (exam == null) {
        throw Exception('Exam not found');
      }
      
      final updatedExam = exam.copyWith(
        isPublished: true,
        status: ExamStatus.scheduled,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedExam.toJson());
      print('✅ ExamRepository: Published exam: $id');
      return updatedExam;
    } catch (e) {
      print('❌ ExamRepository: Error publishing exam: $e');
      throw Exception('Failed to publish exam: $e');
    }
  }

  /// Update exam status
  Future<Exam> updateExamStatus(String id, ExamStatus status) async {
    try {
      print('🎯 ExamRepository: Updating exam status: $id to ${status.name}');
      final exam = await getExamById(id);
      if (exam == null) {
        throw Exception('Exam not found');
      }
      
      final updatedExam = exam.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedExam.toJson());
      print('✅ ExamRepository: Updated exam status: $id');
      return updatedExam;
    } catch (e) {
      print('❌ ExamRepository: Error updating exam status: $e');
      throw Exception('Failed to update exam status: $e');
    }
  }

  /// Get exams by status
  Future<List<Exam>> getExamsByStatus(ExamStatus status) async {
    try {
      print('🔍 ExamRepository: Fetching exams with status: ${status.name}');
      final querySnapshot = await _collection
          .where('status', isEqualTo: status.name)
          .orderBy('startDateTime', descending: true)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ ExamRepository: Retrieved ${exams.length} exams with status ${status.name}');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exams by status: $e');
      throw Exception('Failed to fetch exams by status: $e');
    }
  }

  /// Get exams by course ID
  Future<List<Exam>> getExamsByCourse(String courseId) async {
    try {
      print('🔍 ExamRepository: Fetching exams for course: $courseId');
      final querySnapshot = await _collection
          .where('courseId', isEqualTo: courseId)
          .orderBy('startDateTime', descending: true)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ ExamRepository: Found ${exams.length} exams for course: $courseId');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exams by course: $e');
      throw Exception('Failed to fetch exams by course: $e');
    }
  }

  /// Get exams by instructor ID
  Future<List<Exam>> getExamsByInstructor(String instructorId) async {
    try {
      print('🔍 ExamRepository: Fetching exams for instructor: $instructorId');
      final querySnapshot = await _collection
          .where('instructorId', isEqualTo: instructorId)
          .orderBy('startDateTime', descending: true)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ ExamRepository: Found ${exams.length} exams for instructor: $instructorId');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exams by instructor: $e');
      throw Exception('Failed to fetch exams by instructor: $e');
    }
  }

  /// Get exams by date range
  Future<List<Exam>> getExamsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      print('🔍 ExamRepository: Fetching exams between $startDate and $endDate');
      final querySnapshot = await _collection
          .where('startDateTime', isGreaterThanOrEqualTo: startDate)
          .where('startDateTime', isLessThanOrEqualTo: endDate)
          .orderBy('startDateTime', descending: true)
          .get();

      final exams = querySnapshot.docs
          .map((doc) => Exam.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ ExamRepository: Found ${exams.length} exams in date range');
      return exams;
    } catch (e) {
      print('❌ ExamRepository: Error fetching exams by date range: $e');
      throw Exception('Failed to fetch exams by date range: $e');
    }
  }

  /// Validate exam data
  Future<bool> validateExam(Exam exam) async {
    try {
      print('🔍 ExamRepository: Validating exam: ${exam.title}');

      // Basic validation checks
      if (exam.title.isEmpty) return false;
      if (exam.startDateTime.isAfter(exam.endDateTime)) return false;
      if (exam.questions.isEmpty) return false;

      print('✅ ExamRepository: Exam validation passed');
      return true;
    } catch (e) {
      print('❌ ExamRepository: Error validating exam: $e');
      return false;
    }
  }

  /// Duplicate an exam
  Future<Exam> duplicateExam(String examId) async {
    try {
      print('🔍 ExamRepository: Duplicating exam: $examId');
      final doc = await _collection.doc(examId).get();

      if (!doc.exists) {
        throw Exception('Exam not found');
      }

      final originalExam = Exam.fromJson(doc.data() as Map<String, dynamic>, doc.id);
      final duplicatedExam = originalExam.copyWith(
        id: '',
        title: '${originalExam.title} (Copy)',
        status: ExamStatus.draft,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final newExam = await createExam(duplicatedExam);
      print('✅ ExamRepository: Exam duplicated successfully');
      return newExam;
    } catch (e) {
      print('❌ ExamRepository: Error duplicating exam: $e');
      throw Exception('Failed to duplicate exam: $e');
    }
  }

  /// Start an exam
  Future<void> startExam(String examId) async {
    try {
      print('🔍 ExamRepository: Starting exam: $examId');
      await _collection.doc(examId).update({
        'status': ExamStatus.active.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('✅ ExamRepository: Exam started successfully');
    } catch (e) {
      print('❌ ExamRepository: Error starting exam: $e');
      throw Exception('Failed to start exam: $e');
    }
  }

  /// End an exam
  Future<void> endExam(String examId) async {
    try {
      print('🔍 ExamRepository: Ending exam: $examId');
      await _collection.doc(examId).update({
        'status': ExamStatus.completed.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('✅ ExamRepository: Exam ended successfully');
    } catch (e) {
      print('❌ ExamRepository: Error ending exam: $e');
      throw Exception('Failed to end exam: $e');
    }
  }
}
