import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/certificate_model.dart';

/// Repository for managing certificates in Firebase
/// 
/// This repository handles:
/// - Certificate CRUD operations
/// - Digital certificate generation
/// - Verification system
/// - Template management
/// - Firebase data synchronization
/// - Integration with existing certificate system
class CertificateRepository {
  /// Firebase Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Collection reference for certificates
  static const String _collectionName = 'certificates';
  
  /// Get collection reference
  CollectionReference get _collection => _firestore.collection(_collectionName);

  /// Create a new certificate
  Future<Certificate> createCertificate(Certificate certificate) async {
    try {
      print('🎯 CertificateRepository: Creating certificate: ${certificate.title}');
      final docRef = await _collection.add(certificate.toJson());
      final createdCertificate = certificate.copyWith(id: docRef.id);
      print('✅ CertificateRepository: Created certificate with ID: ${docRef.id}');
      return createdCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error creating certificate: $e');
      throw Exception('Failed to create certificate: $e');
    }
  }

  /// Get all certificates
  Future<List<Certificate>> getAllCertificates() async {
    try {
      print('🔍 CertificateRepository: Fetching all certificates');
      final querySnapshot = await _collection
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates: $e');
      throw Exception('Failed to fetch certificates: $e');
    }
  }

  /// Get certificate by ID
  Future<Certificate?> getCertificateById(String id) async {
    try {
      print('🔍 CertificateRepository: Fetching certificate with ID: $id');
      final docSnapshot = await _collection.doc(id).get();
      
      if (!docSnapshot.exists) {
        print('⚠️ CertificateRepository: Certificate not found with ID: $id');
        return null;
      }
      
      final certificate = Certificate.fromJson(
        docSnapshot.data() as Map<String, dynamic>,
        docSnapshot.id,
      );
      
      print('✅ CertificateRepository: Retrieved certificate: ${certificate.title}');
      return certificate;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificate: $e');
      throw Exception('Failed to fetch certificate: $e');
    }
  }

  /// Update an existing certificate
  Future<Certificate> updateCertificate(Certificate certificate) async {
    try {
      print('🎯 CertificateRepository: Updating certificate: ${certificate.title}');
      final updatedCertificate = certificate.copyWith(updatedAt: DateTime.now());
      await _collection.doc(certificate.id).update(updatedCertificate.toJson());
      print('✅ CertificateRepository: Updated certificate: ${certificate.title}');
      return updatedCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error updating certificate: $e');
      throw Exception('Failed to update certificate: $e');
    }
  }

  /// Delete a certificate
  Future<void> deleteCertificate(String id) async {
    try {
      print('🎯 CertificateRepository: Deleting certificate with ID: $id');
      await _collection.doc(id).delete();
      print('✅ CertificateRepository: Deleted certificate with ID: $id');
    } catch (e) {
      print('❌ CertificateRepository: Error deleting certificate: $e');
      throw Exception('Failed to delete certificate: $e');
    }
  }

  /// Get certificates by student ID
  Future<List<Certificate>> getCertificatesByStudentId(String studentId) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates for student: $studentId');
      final querySnapshot = await _collection
          .where('studentId', isEqualTo: studentId)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates for student $studentId');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by student: $e');
      throw Exception('Failed to fetch certificates by student: $e');
    }
  }

  /// Get certificates by course ID
  Future<List<Certificate>> getCertificatesByCourseId(String courseId) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates for course: $courseId');
      final querySnapshot = await _collection
          .where('courseId', isEqualTo: courseId)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates for course $courseId');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by course: $e');
      throw Exception('Failed to fetch certificates by course: $e');
    }
  }

  /// Get certificates by type
  Future<List<Certificate>> getCertificatesByType(CertificateType type) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates with type: ${type.name}');
      final querySnapshot = await _collection
          .where('type', isEqualTo: type.name)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates with type ${type.name}');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by type: $e');
      throw Exception('Failed to fetch certificates by type: $e');
    }
  }

  /// Get certificates by status
  Future<List<Certificate>> getCertificatesByStatus(CertificateStatus status) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates with status: ${status.name}');
      final querySnapshot = await _collection
          .where('status', isEqualTo: status.name)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates with status ${status.name}');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by status: $e');
      throw Exception('Failed to fetch certificates by status: $e');
    }
  }

  /// Verify certificate by certificate number
  Future<Certificate?> verifyCertificate(String certificateNumber) async {
    try {
      print('🔍 CertificateRepository: Verifying certificate: $certificateNumber');
      final querySnapshot = await _collection
          .where('certificateNumber', isEqualTo: certificateNumber)
          .where('status', isEqualTo: CertificateStatus.active.name)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty) {
        print('⚠️ CertificateRepository: Certificate not found or invalid: $certificateNumber');
        return null;
      }
      
      final certificate = Certificate.fromJson(
        querySnapshot.docs.first.data() as Map<String, dynamic>,
        querySnapshot.docs.first.id,
      );
      
      print('✅ CertificateRepository: Certificate verified: ${certificate.title}');
      return certificate;
    } catch (e) {
      print('❌ CertificateRepository: Error verifying certificate: $e');
      throw Exception('Failed to verify certificate: $e');
    }
  }

  /// Search certificates
  Future<List<Certificate>> searchCertificates(String searchTerm) async {
    try {
      print('🔍 CertificateRepository: Searching certificates with term: $searchTerm');
      
      // Search by student name
      final nameQuery = await _collection
          .where('studentName', isGreaterThanOrEqualTo: searchTerm)
          .where('studentName', isLessThan: searchTerm + 'z')
          .get();
      
      // Search by certificate title
      final titleQuery = await _collection
          .where('title', isGreaterThanOrEqualTo: searchTerm)
          .where('title', isLessThan: searchTerm + 'z')
          .get();
      
      final certificates = <Certificate>[];
      final addedIds = <String>{};
      
      // Add results from name search
      for (final doc in nameQuery.docs) {
        if (!addedIds.contains(doc.id)) {
          certificates.add(Certificate.fromJson(
            doc.data() as Map<String, dynamic>,
            doc.id,
          ));
          addedIds.add(doc.id);
        }
      }
      
      // Add results from title search
      for (final doc in titleQuery.docs) {
        if (!addedIds.contains(doc.id)) {
          certificates.add(Certificate.fromJson(
            doc.data() as Map<String, dynamic>,
            doc.id,
          ));
          addedIds.add(doc.id);
        }
      }
      
      // Sort by issue date
      certificates.sort((a, b) => b.issueDate.compareTo(a.issueDate));
      
      print('✅ CertificateRepository: Found ${certificates.length} certificates matching "$searchTerm"');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error searching certificates: $e');
      throw Exception('Failed to search certificates: $e');
    }
  }

  /// Update certificate status
  Future<Certificate> updateCertificateStatus(String id, CertificateStatus status) async {
    try {
      print('🎯 CertificateRepository: Updating certificate status: $id to ${status.name}');
      final certificate = await getCertificateById(id);
      if (certificate == null) {
        throw Exception('Certificate not found');
      }
      
      final updatedCertificate = certificate.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedCertificate.toJson());
      print('✅ CertificateRepository: Updated certificate status: $id');
      return updatedCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error updating certificate status: $e');
      throw Exception('Failed to update certificate status: $e');
    }
  }

  /// Mark certificate as verified
  Future<Certificate> verifyCertificateById(String id, String verifiedBy) async {
    try {
      print('🎯 CertificateRepository: Marking certificate as verified: $id');
      final certificate = await getCertificateById(id);
      if (certificate == null) {
        throw Exception('Certificate not found');
      }
      
      final updatedCertificate = certificate.copyWith(
        isVerified: true,
        verifiedAt: DateTime.now(),
        verifiedBy: verifiedBy,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedCertificate.toJson());
      print('✅ CertificateRepository: Certificate marked as verified: $id');
      return updatedCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error verifying certificate: $e');
      throw Exception('Failed to verify certificate: $e');
    }
  }
}
