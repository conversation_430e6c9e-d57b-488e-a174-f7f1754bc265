import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/certificate_model.dart';

/// Repository for managing certificates in Firebase
/// 
/// This repository handles:
/// - Certificate CRUD operations
/// - Digital certificate generation
/// - Verification system
/// - Template management
/// - Firebase data synchronization
/// - Integration with existing certificate system
class CertificateRepository {
  /// Firebase Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Collection reference for certificates
  static const String _collectionName = 'certificates';
  
  /// Get collection reference
  CollectionReference get _collection => _firestore.collection(_collectionName);

  /// Create a new certificate
  Future<Certificate> createCertificate(Certificate certificate) async {
    try {
      print('🎯 CertificateRepository: Creating certificate: ${certificate.title}');
      final docRef = await _collection.add(certificate.toJson());
      final createdCertificate = certificate.copyWith(id: docRef.id);
      print('✅ CertificateRepository: Created certificate with ID: ${docRef.id}');
      return createdCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error creating certificate: $e');
      throw Exception('Failed to create certificate: $e');
    }
  }

  /// Get all certificates
  Future<List<Certificate>> getAllCertificates() async {
    try {
      print('🔍 CertificateRepository: Fetching all certificates');
      final querySnapshot = await _collection
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates: $e');
      throw Exception('Failed to fetch certificates: $e');
    }
  }

  /// Get certificate by ID
  Future<Certificate?> getCertificateById(String id) async {
    try {
      print('🔍 CertificateRepository: Fetching certificate with ID: $id');
      final docSnapshot = await _collection.doc(id).get();
      
      if (!docSnapshot.exists) {
        print('⚠️ CertificateRepository: Certificate not found with ID: $id');
        return null;
      }
      
      final certificate = Certificate.fromJson(
        docSnapshot.data() as Map<String, dynamic>,
        docSnapshot.id,
      );
      
      print('✅ CertificateRepository: Retrieved certificate: ${certificate.title}');
      return certificate;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificate: $e');
      throw Exception('Failed to fetch certificate: $e');
    }
  }

  /// Update an existing certificate
  Future<Certificate> updateCertificate(Certificate certificate) async {
    try {
      print('🎯 CertificateRepository: Updating certificate: ${certificate.title}');
      final updatedCertificate = certificate.copyWith(updatedAt: DateTime.now());
      await _collection.doc(certificate.id).update(updatedCertificate.toJson());
      print('✅ CertificateRepository: Updated certificate: ${certificate.title}');
      return updatedCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error updating certificate: $e');
      throw Exception('Failed to update certificate: $e');
    }
  }

  /// Delete a certificate
  Future<void> deleteCertificate(String id) async {
    try {
      print('🎯 CertificateRepository: Deleting certificate with ID: $id');
      await _collection.doc(id).delete();
      print('✅ CertificateRepository: Deleted certificate with ID: $id');
    } catch (e) {
      print('❌ CertificateRepository: Error deleting certificate: $e');
      throw Exception('Failed to delete certificate: $e');
    }
  }

  /// Get certificates by student ID
  Future<List<Certificate>> getCertificatesByStudentId(String studentId) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates for student: $studentId');
      final querySnapshot = await _collection
          .where('studentId', isEqualTo: studentId)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates for student $studentId');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by student: $e');
      throw Exception('Failed to fetch certificates by student: $e');
    }
  }

  /// Get certificates by course ID
  Future<List<Certificate>> getCertificatesByCourseId(String courseId) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates for course: $courseId');
      final querySnapshot = await _collection
          .where('courseId', isEqualTo: courseId)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates for course $courseId');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by course: $e');
      throw Exception('Failed to fetch certificates by course: $e');
    }
  }

  /// Get certificates by type
  Future<List<Certificate>> getCertificatesByType(CertificateType type) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates with type: ${type.name}');
      final querySnapshot = await _collection
          .where('type', isEqualTo: type.name)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates with type ${type.name}');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by type: $e');
      throw Exception('Failed to fetch certificates by type: $e');
    }
  }

  /// Get certificates by status
  Future<List<Certificate>> getCertificatesByStatus(CertificateStatus status) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates with status: ${status.name}');
      final querySnapshot = await _collection
          .where('status', isEqualTo: status.name)
          .orderBy('issueDate', descending: true)
          .get();
      
      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ CertificateRepository: Retrieved ${certificates.length} certificates with status ${status.name}');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by status: $e');
      throw Exception('Failed to fetch certificates by status: $e');
    }
  }

  /// Verify certificate by certificate number
  Future<Certificate?> verifyCertificate(String certificateNumber) async {
    try {
      print('🔍 CertificateRepository: Verifying certificate: $certificateNumber');
      final querySnapshot = await _collection
          .where('certificateNumber', isEqualTo: certificateNumber)
          .where('status', isEqualTo: CertificateStatus.active.name)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty) {
        print('⚠️ CertificateRepository: Certificate not found or invalid: $certificateNumber');
        return null;
      }
      
      final certificate = Certificate.fromJson(
        querySnapshot.docs.first.data() as Map<String, dynamic>,
        querySnapshot.docs.first.id,
      );
      
      print('✅ CertificateRepository: Certificate verified: ${certificate.title}');
      return certificate;
    } catch (e) {
      print('❌ CertificateRepository: Error verifying certificate: $e');
      throw Exception('Failed to verify certificate: $e');
    }
  }

  /// Search certificates
  Future<List<Certificate>> searchCertificates(String searchTerm) async {
    try {
      print('🔍 CertificateRepository: Searching certificates with term: $searchTerm');
      
      // Search by student name
      final nameQuery = await _collection
          .where('studentName', isGreaterThanOrEqualTo: searchTerm)
          .where('studentName', isLessThan: searchTerm + 'z')
          .get();
      
      // Search by certificate title
      final titleQuery = await _collection
          .where('title', isGreaterThanOrEqualTo: searchTerm)
          .where('title', isLessThan: searchTerm + 'z')
          .get();
      
      final certificates = <Certificate>[];
      final addedIds = <String>{};
      
      // Add results from name search
      for (final doc in nameQuery.docs) {
        if (!addedIds.contains(doc.id)) {
          certificates.add(Certificate.fromJson(
            doc.data() as Map<String, dynamic>,
            doc.id,
          ));
          addedIds.add(doc.id);
        }
      }
      
      // Add results from title search
      for (final doc in titleQuery.docs) {
        if (!addedIds.contains(doc.id)) {
          certificates.add(Certificate.fromJson(
            doc.data() as Map<String, dynamic>,
            doc.id,
          ));
          addedIds.add(doc.id);
        }
      }
      
      // Sort by issue date
      certificates.sort((a, b) => b.issueDate.compareTo(a.issueDate));
      
      print('✅ CertificateRepository: Found ${certificates.length} certificates matching "$searchTerm"');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error searching certificates: $e');
      throw Exception('Failed to search certificates: $e');
    }
  }

  /// Update certificate status
  Future<Certificate> updateCertificateStatus(String id, CertificateStatus status) async {
    try {
      print('🎯 CertificateRepository: Updating certificate status: $id to ${status.name}');
      final certificate = await getCertificateById(id);
      if (certificate == null) {
        throw Exception('Certificate not found');
      }
      
      final updatedCertificate = certificate.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedCertificate.toJson());
      print('✅ CertificateRepository: Updated certificate status: $id');
      return updatedCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error updating certificate status: $e');
      throw Exception('Failed to update certificate status: $e');
    }
  }

  /// Mark certificate as verified
  Future<Certificate> verifyCertificateById(String id, String verifiedBy) async {
    try {
      print('🎯 CertificateRepository: Marking certificate as verified: $id');
      final certificate = await getCertificateById(id);
      if (certificate == null) {
        throw Exception('Certificate not found');
      }

      final updatedCertificate = certificate.copyWith(
        isVerified: true,
        verifiedAt: DateTime.now(),
        verifiedBy: verifiedBy,
        updatedAt: DateTime.now(),
      );

      await _collection.doc(id).update(updatedCertificate.toJson());
      print('✅ CertificateRepository: Certificate marked as verified: $id');
      return updatedCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error verifying certificate: $e');
      throw Exception('Failed to verify certificate: $e');
    }
  }

  /// Get certificates by student ID
  Future<List<Certificate>> getCertificatesByStudent(String studentId) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates for student: $studentId');
      final querySnapshot = await _collection
          .where('studentId', isEqualTo: studentId)
          .orderBy('issueDate', descending: true)
          .get();

      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ CertificateRepository: Found ${certificates.length} certificates for student');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by student: $e');
      throw Exception('Failed to fetch certificates by student: $e');
    }
  }

  /// Get certificates by course ID
  Future<List<Certificate>> getCertificatesByCourse(String courseId) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates for course: $courseId');
      final querySnapshot = await _collection
          .where('courseId', isEqualTo: courseId)
          .orderBy('issueDate', descending: true)
          .get();

      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ CertificateRepository: Found ${certificates.length} certificates for course');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by course: $e');
      throw Exception('Failed to fetch certificates by course: $e');
    }
  }

  /// Get certificates by date range
  Future<List<Certificate>> getCertificatesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      print('🔍 CertificateRepository: Fetching certificates between $startDate and $endDate');
      final querySnapshot = await _collection
          .where('issueDate', isGreaterThanOrEqualTo: startDate)
          .where('issueDate', isLessThanOrEqualTo: endDate)
          .orderBy('issueDate', descending: true)
          .get();

      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      print('✅ CertificateRepository: Found ${certificates.length} certificates in date range');
      return certificates;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching certificates by date range: $e');
      throw Exception('Failed to fetch certificates by date range: $e');
    }
  }

  /// Generate certificate PDF
  Future<String> generateCertificatePDF(Certificate certificate) async {
    try {
      print('🔍 CertificateRepository: Generating PDF for certificate: ${certificate.id}');

      // This would integrate with your PDF generation service
      // For now, return a placeholder path
      final pdfPath = 'certificates/${certificate.id}.pdf';

      print('✅ CertificateRepository: PDF generated successfully');
      return pdfPath;
    } catch (e) {
      print('❌ CertificateRepository: Error generating PDF: $e');
      throw Exception('Failed to generate certificate PDF: $e');
    }
  }

  /// Validate certificate
  Future<bool> validateCertificate(String certificateId, String verificationCode) async {
    try {
      print('🔍 CertificateRepository: Validating certificate: $certificateId');
      final doc = await _collection.doc(certificateId).get();

      if (!doc.exists) return false;

      final certificate = Certificate.fromJson(doc.data() as Map<String, dynamic>, doc.id);
      final isValid = certificate.certificateNumber == verificationCode &&
                     certificate.status == CertificateStatus.active;

      print('✅ CertificateRepository: Certificate validation result: $isValid');
      return isValid;
    } catch (e) {
      print('❌ CertificateRepository: Error validating certificate: $e');
      return false;
    }
  }

  /// Issue certificate
  Future<Certificate> issueCertificate(String certificateId) async {
    try {
      print('🔍 CertificateRepository: Issuing certificate: $certificateId');
      await _collection.doc(certificateId).update({
        'status': CertificateStatus.active.name,
        'issueDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final doc = await _collection.doc(certificateId).get();
      final certificate = Certificate.fromJson(doc.data() as Map<String, dynamic>, doc.id);

      print('✅ CertificateRepository: Certificate issued successfully');
      return certificate;
    } catch (e) {
      print('❌ CertificateRepository: Error issuing certificate: $e');
      throw Exception('Failed to issue certificate: $e');
    }
  }

  /// Revoke certificate
  Future<Certificate> revokeCertificate(String certificateId, String reason) async {
    try {
      print('🔍 CertificateRepository: Revoking certificate: $certificateId');
      await _collection.doc(certificateId).update({
        'status': CertificateStatus.revoked.name,
        'revokedAt': FieldValue.serverTimestamp(),
        'revocationReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final doc = await _collection.doc(certificateId).get();
      final certificate = Certificate.fromJson(doc.data() as Map<String, dynamic>, doc.id);

      print('✅ CertificateRepository: Certificate revoked successfully');
      return certificate;
    } catch (e) {
      print('❌ CertificateRepository: Error revoking certificate: $e');
      throw Exception('Failed to revoke certificate: $e');
    }
  }

  /// Duplicate certificate
  Future<Certificate> duplicateCertificate(String certificateId) async {
    try {
      print('🔍 CertificateRepository: Duplicating certificate: $certificateId');
      final doc = await _collection.doc(certificateId).get();

      if (!doc.exists) {
        throw Exception('Certificate not found');
      }

      final originalCertificate = Certificate.fromJson(doc.data() as Map<String, dynamic>, doc.id);
      final duplicatedCertificate = originalCertificate.copyWith(
        id: '',
        title: '${originalCertificate.title} (Copy)',
        status: CertificateStatus.pending,
        issueDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final newCertificate = await createCertificate(duplicatedCertificate);
      print('✅ CertificateRepository: Certificate duplicated successfully');
      return newCertificate;
    } catch (e) {
      print('❌ CertificateRepository: Error duplicating certificate: $e');
      throw Exception('Failed to duplicate certificate: $e');
    }
  }

  /// Bulk generate certificates
  Future<List<Certificate>> bulkGenerateCertificates(List<Certificate> certificates) async {
    try {
      print('🔍 CertificateRepository: Bulk generating ${certificates.length} certificates');
      final List<Certificate> createdCertificates = [];

      for (Certificate certificate in certificates) {
        final created = await createCertificate(certificate);
        createdCertificates.add(created);
      }

      print('✅ CertificateRepository: Bulk generation completed');
      return createdCertificates;
    } catch (e) {
      print('❌ CertificateRepository: Error in bulk generation: $e');
      throw Exception('Failed to bulk generate certificates: $e');
    }
  }

  /// Export certificates
  Future<String> exportCertificates(List<String> certificateIds, String format) async {
    try {
      print('🔍 CertificateRepository: Exporting ${certificateIds.length} certificates as $format');

      // This would integrate with your export service
      // For now, return a placeholder path
      final exportPath = 'exports/certificates_${DateTime.now().millisecondsSinceEpoch}.$format';

      print('✅ CertificateRepository: Export completed');
      return exportPath;
    } catch (e) {
      print('❌ CertificateRepository: Error exporting certificates: $e');
      throw Exception('Failed to export certificates: $e');
    }
  }

  /// Send certificate email
  Future<void> sendCertificateEmail(String certificateId, String recipientEmail) async {
    try {
      print('🔍 CertificateRepository: Sending certificate email to: $recipientEmail');

      // This would integrate with your email service
      // For now, just log the action

      print('✅ CertificateRepository: Certificate email sent successfully');
    } catch (e) {
      print('❌ CertificateRepository: Error sending certificate email: $e');
      throw Exception('Failed to send certificate email: $e');
    }
  }

  /// Get certificate statistics
  Future<Map<String, dynamic>> getCertificateStatistics() async {
    try {
      print('🔍 CertificateRepository: Fetching certificate statistics');
      final querySnapshot = await _collection.get();

      final certificates = querySnapshot.docs
          .map((doc) => Certificate.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();

      final stats = {
        'total': certificates.length,
        'issued': certificates.where((cert) => cert.status == CertificateStatus.issued).length,
        'pending': certificates.where((cert) => cert.status == CertificateStatus.pending).length,
        'revoked': certificates.where((cert) => cert.status == CertificateStatus.revoked).length,
        'thisMonth': certificates.where((cert) =>
          cert.status == CertificateStatus.active &&
          cert.issueDate.month == DateTime.now().month &&
          cert.issueDate.year == DateTime.now().year
        ).length,
      };

      print('✅ CertificateRepository: Statistics fetched successfully');
      return stats;
    } catch (e) {
      print('❌ CertificateRepository: Error fetching statistics: $e');
      throw Exception('Failed to fetch certificate statistics: $e');
    }
  }
}
