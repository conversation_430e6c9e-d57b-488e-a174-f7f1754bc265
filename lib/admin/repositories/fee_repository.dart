import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/fee_structure_model.dart';

/// Repository for managing fee structures in Firebase
/// 
/// This repository handles:
/// - Fee structure CRUD operations
/// - Academic year and grade level filtering
/// - Discount management
/// - Firebase data synchronization
/// - Error handling and data validation
class FeeRepository {
  /// Firebase Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Collection reference for fee structures
  static const String _collectionName = 'fee_structures';
  
  /// Get collection reference
  CollectionReference get _collection => _firestore.collection(_collectionName);

  /// Create a new fee structure
  /// 
  /// Takes a [FeeStructure] object and creates it in Firebase.
  /// Returns the created fee structure with the assigned ID.
  /// Throws an exception if the operation fails.
  Future<FeeStructure> createFeeStructure(FeeStructure feeStructure) async {
    try {
      print('🎯 FeeRepository: Creating fee structure: ${feeStructure.name}');
      
      // Create fee structure document
      final docRef = await _collection.add(feeStructure.toJson());
      
      // Return fee structure with assigned ID
      final createdFeeStructure = feeStructure.copyWith(id: docRef.id);
      
      print('✅ FeeRepository: Created fee structure with ID: ${docRef.id}');
      return createdFeeStructure;
      
    } catch (e) {
      print('❌ FeeRepository: Error creating fee structure: $e');
      throw Exception('Failed to create fee structure: $e');
    }
  }

  /// Get all fee structures
  /// 
  /// Retrieves all fee structures from Firebase.
  /// Returns a list of [FeeStructure] objects.
  /// Throws an exception if the operation fails.
  Future<List<FeeStructure>> getAllFeeStructures() async {
    try {
      print('🔍 FeeRepository: Fetching all fee structures');
      
      final querySnapshot = await _collection
          .orderBy('createdAt', descending: true)
          .get();
      
      final feeStructures = querySnapshot.docs
          .map((doc) => FeeStructure.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ FeeRepository: Retrieved ${feeStructures.length} fee structures');
      return feeStructures;
      
    } catch (e) {
      print('❌ FeeRepository: Error fetching fee structures: $e');
      throw Exception('Failed to fetch fee structures: $e');
    }
  }

  /// Get fee structure by ID
  /// 
  /// Retrieves a specific fee structure by its ID.
  /// Returns a [FeeStructure] object or null if not found.
  /// Throws an exception if the operation fails.
  Future<FeeStructure?> getFeeStructureById(String id) async {
    try {
      print('🔍 FeeRepository: Fetching fee structure with ID: $id');
      
      final docSnapshot = await _collection.doc(id).get();
      
      if (!docSnapshot.exists) {
        print('⚠️ FeeRepository: Fee structure not found with ID: $id');
        return null;
      }
      
      final feeStructure = FeeStructure.fromJson(
        docSnapshot.data() as Map<String, dynamic>,
        docSnapshot.id,
      );
      
      print('✅ FeeRepository: Retrieved fee structure: ${feeStructure.name}');
      return feeStructure;
      
    } catch (e) {
      print('❌ FeeRepository: Error fetching fee structure: $e');
      throw Exception('Failed to fetch fee structure: $e');
    }
  }

  /// Update an existing fee structure
  /// 
  /// Takes a [FeeStructure] object and updates it in Firebase.
  /// Returns the updated fee structure.
  /// Throws an exception if the operation fails.
  Future<FeeStructure> updateFeeStructure(FeeStructure feeStructure) async {
    try {
      print('🎯 FeeRepository: Updating fee structure: ${feeStructure.name}');
      
      final updatedFeeStructure = feeStructure.copyWith(updatedAt: DateTime.now());
      
      await _collection.doc(feeStructure.id).update(updatedFeeStructure.toJson());
      
      print('✅ FeeRepository: Updated fee structure: ${feeStructure.name}');
      return updatedFeeStructure;
      
    } catch (e) {
      print('❌ FeeRepository: Error updating fee structure: $e');
      throw Exception('Failed to update fee structure: $e');
    }
  }

  /// Delete a fee structure
  /// 
  /// Deletes a fee structure from Firebase by its ID.
  /// Throws an exception if the operation fails.
  Future<void> deleteFeeStructure(String id) async {
    try {
      print('🎯 FeeRepository: Deleting fee structure with ID: $id');
      
      await _collection.doc(id).delete();
      
      print('✅ FeeRepository: Deleted fee structure with ID: $id');
      
    } catch (e) {
      print('❌ FeeRepository: Error deleting fee structure: $e');
      throw Exception('Failed to delete fee structure: $e');
    }
  }

  /// Get fee structures by academic year
  /// 
  /// Retrieves fee structures for a specific academic year.
  /// Returns a list of [FeeStructure] objects.
  /// Throws an exception if the operation fails.
  Future<List<FeeStructure>> getFeeStructuresByAcademicYear(String academicYear) async {
    try {
      print('🔍 FeeRepository: Fetching fee structures for academic year: $academicYear');
      
      final querySnapshot = await _collection
          .where('academicYear', isEqualTo: academicYear)
          .orderBy('gradeLevel')
          .get();
      
      final feeStructures = querySnapshot.docs
          .map((doc) => FeeStructure.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ FeeRepository: Retrieved ${feeStructures.length} fee structures for $academicYear');
      return feeStructures;
      
    } catch (e) {
      print('❌ FeeRepository: Error fetching fee structures by academic year: $e');
      throw Exception('Failed to fetch fee structures by academic year: $e');
    }
  }

  /// Get fee structures by grade level
  /// 
  /// Retrieves fee structures for a specific grade level.
  /// Returns a list of [FeeStructure] objects.
  /// Throws an exception if the operation fails.
  Future<List<FeeStructure>> getFeeStructuresByGradeLevel(String gradeLevel) async {
    try {
      print('🔍 FeeRepository: Fetching fee structures for grade level: $gradeLevel');
      
      final querySnapshot = await _collection
          .where('gradeLevel', isEqualTo: gradeLevel)
          .where('isActive', isEqualTo: true)
          .orderBy('academicYear', descending: true)
          .get();
      
      final feeStructures = querySnapshot.docs
          .map((doc) => FeeStructure.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ FeeRepository: Retrieved ${feeStructures.length} fee structures for grade $gradeLevel');
      return feeStructures;
      
    } catch (e) {
      print('❌ FeeRepository: Error fetching fee structures by grade level: $e');
      throw Exception('Failed to fetch fee structures by grade level: $e');
    }
  }

  /// Get active fee structures
  /// 
  /// Retrieves all active fee structures.
  /// Returns a list of [FeeStructure] objects.
  /// Throws an exception if the operation fails.
  Future<List<FeeStructure>> getActiveFeeStructures() async {
    try {
      print('🔍 FeeRepository: Fetching active fee structures');
      
      final querySnapshot = await _collection
          .where('isActive', isEqualTo: true)
          .orderBy('academicYear', descending: true)
          .orderBy('gradeLevel')
          .get();
      
      final feeStructures = querySnapshot.docs
          .map((doc) => FeeStructure.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ FeeRepository: Retrieved ${feeStructures.length} active fee structures');
      return feeStructures;
      
    } catch (e) {
      print('❌ FeeRepository: Error fetching active fee structures: $e');
      throw Exception('Failed to fetch active fee structures: $e');
    }
  }

  /// Search fee structures by name
  /// 
  /// Searches for fee structures by name using case-insensitive matching.
  /// Returns a list of [FeeStructure] objects.
  /// Throws an exception if the operation fails.
  Future<List<FeeStructure>> searchFeeStructures(String searchTerm) async {
    try {
      print('🔍 FeeRepository: Searching fee structures with term: $searchTerm');
      
      final querySnapshot = await _collection
          .orderBy('name')
          .startAt([searchTerm.toLowerCase()])
          .endAt([searchTerm.toLowerCase() + '\uf8ff'])
          .get();
      
      final feeStructures = querySnapshot.docs
          .map((doc) => FeeStructure.fromJson(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
      
      print('✅ FeeRepository: Found ${feeStructures.length} fee structures matching "$searchTerm"');
      return feeStructures;
      
    } catch (e) {
      print('❌ FeeRepository: Error searching fee structures: $e');
      throw Exception('Failed to search fee structures: $e');
    }
  }

  /// Toggle fee structure active status
  /// 
  /// Toggles the active status of a fee structure.
  /// Returns the updated fee structure.
  /// Throws an exception if the operation fails.
  Future<FeeStructure> toggleFeeStructureStatus(String id) async {
    try {
      print('🎯 FeeRepository: Toggling status for fee structure: $id');
      
      final feeStructure = await getFeeStructureById(id);
      if (feeStructure == null) {
        throw Exception('Fee structure not found');
      }
      
      final updatedFeeStructure = feeStructure.copyWith(
        isActive: !feeStructure.isActive,
        updatedAt: DateTime.now(),
      );
      
      await _collection.doc(id).update(updatedFeeStructure.toJson());
      
      print('✅ FeeRepository: Toggled status for fee structure: $id');
      return updatedFeeStructure;
      
    } catch (e) {
      print('❌ FeeRepository: Error toggling fee structure status: $e');
      throw Exception('Failed to toggle fee structure status: $e');
    }
  }

  /// Get fee structure for specific student
  /// 
  /// Gets the applicable fee structure for a student based on their grade level and academic year.
  /// Returns a [FeeStructure] object or null if not found.
  /// Throws an exception if the operation fails.
  Future<FeeStructure?> getFeeStructureForStudent(String gradeLevel, String academicYear) async {
    try {
      print('🔍 FeeRepository: Fetching fee structure for grade $gradeLevel, year $academicYear');
      
      final querySnapshot = await _collection
          .where('gradeLevel', isEqualTo: gradeLevel)
          .where('academicYear', isEqualTo: academicYear)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty) {
        print('⚠️ FeeRepository: No fee structure found for grade $gradeLevel, year $academicYear');
        return null;
      }
      
      final feeStructure = FeeStructure.fromJson(
        querySnapshot.docs.first.data() as Map<String, dynamic>,
        querySnapshot.docs.first.id,
      );
      
      print('✅ FeeRepository: Retrieved fee structure for student: ${feeStructure.name}');
      return feeStructure;
      
    } catch (e) {
      print('❌ FeeRepository: Error fetching fee structure for student: $e');
      throw Exception('Failed to fetch fee structure for student: $e');
    }
  }
}
