import 'package:flutter_bloc/flutter_bloc.dart';
import '../../repositories/certificate_repository.dart';
import '../../models/certificate_model.dart';
import 'certificate_event.dart';
import 'certificate_state.dart';

/// BLoC for managing certificate operations
/// 
/// This BLoC handles all certificate related operations including:
/// - Loading certificates from Firebase
/// - Creating, updating, and deleting certificates
/// - Filtering and searching certificates
/// - Certificate verification and validation
/// - PDF generation and email sending
/// - Bulk operations and statistics
/// 
/// Uses the Repository pattern to interact with Firebase through CertificateRepository.
class CertificateBloc extends Bloc<CertificateEvent, CertificateState> {
  /// Repository for certificate operations
  final CertificateRepository _certificateRepository;

  /// Constructor that initializes the BLoC with CertificateInitial state
  CertificateBloc({required CertificateRepository certificateRepository})
      : _certificateRepository = certificateRepository,
        super(const CertificateInitial()) {
    
    // Register event handlers
    on<LoadCertificates>(_onLoadCertificates);
    on<LoadCertificatesByStudent>(_onLoadCertificatesByStudent);
    on<LoadCertificatesByCourse>(_onLoadCertificatesByCourse);
    on<LoadCertificatesByType>(_onLoadCertificatesByType);
    on<LoadCertificatesByStatus>(_onLoadCertificatesByStatus);
    on<LoadCertificatesByDateRange>(_onLoadCertificatesByDateRange);
    on<CreateCertificate>(_onCreateCertificate);
    on<UpdateCertificate>(_onUpdateCertificate);
    on<DeleteCertificate>(_onDeleteCertificate);
    on<SearchCertificates>(_onSearchCertificates);
    on<VerifyCertificate>(_onVerifyCertificate);
    on<GenerateCertificatePDF>(_onGenerateCertificatePDF);
    on<UpdateCertificateStatus>(_onUpdateCertificateStatus);
    on<SelectCertificate>(_onSelectCertificate);
    on<ClearCertificates>(_onClearCertificates);
    on<RefreshCertificates>(_onRefreshCertificates);
    on<ValidateCertificate>(_onValidateCertificate);
    on<IssueCertificate>(_onIssueCertificate);
    on<RevokeCertificate>(_onRevokeCertificate);
    on<DuplicateCertificate>(_onDuplicateCertificate);
    on<BulkGenerateCertificates>(_onBulkGenerateCertificates);
    on<ExportCertificates>(_onExportCertificates);
    on<SendCertificateEmail>(_onSendCertificateEmail);
    on<GetCertificateStatistics>(_onGetCertificateStatistics);
  }

  /// Handle loading all certificates
  Future<void> _onLoadCertificates(
    LoadCertificates event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.getAllCertificates();
      
      if (certificates.isEmpty) {
        emit(const CertificateEmpty(message: 'No certificates found'));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'all',
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to load certificates: ${e.toString()}',
        failedOperation: 'load_all',
      ));
    }
  }

  /// Handle loading certificates by student
  Future<void> _onLoadCertificatesByStudent(
    LoadCertificatesByStudent event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.getCertificatesByStudent(event.studentId);
      
      if (certificates.isEmpty) {
        emit(CertificateEmpty(
          message: 'No certificates found for this student',
          criteria: event.studentId,
        ));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'student',
          filterValue: event.studentId,
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to load certificates by student: ${e.toString()}',
        failedOperation: 'load_by_student',
      ));
    }
  }

  /// Handle loading certificates by course
  Future<void> _onLoadCertificatesByCourse(
    LoadCertificatesByCourse event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.getCertificatesByCourse(event.courseId);
      
      if (certificates.isEmpty) {
        emit(CertificateEmpty(
          message: 'No certificates found for this course',
          criteria: event.courseId,
        ));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'course',
          filterValue: event.courseId,
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to load certificates by course: ${e.toString()}',
        failedOperation: 'load_by_course',
      ));
    }
  }

  /// Handle loading certificates by type
  Future<void> _onLoadCertificatesByType(
    LoadCertificatesByType event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.getCertificatesByType(event.type);
      
      if (certificates.isEmpty) {
        emit(CertificateEmpty(
          message: 'No certificates found with type: ${event.type.name}',
          criteria: event.type.name,
        ));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'type',
          filterValue: event.type.name,
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to load certificates by type: ${e.toString()}',
        failedOperation: 'load_by_type',
      ));
    }
  }

  /// Handle loading certificates by status
  Future<void> _onLoadCertificatesByStatus(
    LoadCertificatesByStatus event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.getCertificatesByStatus(event.status);
      
      if (certificates.isEmpty) {
        emit(CertificateEmpty(
          message: 'No certificates found with status: ${event.status.name}',
          criteria: event.status.name,
        ));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'status',
          filterValue: event.status.name,
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to load certificates by status: ${e.toString()}',
        failedOperation: 'load_by_status',
      ));
    }
  }

  /// Handle loading certificates by date range
  Future<void> _onLoadCertificatesByDateRange(
    LoadCertificatesByDateRange event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.getCertificatesByDateRange(
        event.startDate,
        event.endDate,
      );
      
      if (certificates.isEmpty) {
        emit(CertificateEmpty(
          message: 'No certificates found in the selected date range',
          criteria: '${event.startDate.toString().split(' ')[0]} - ${event.endDate.toString().split(' ')[0]}',
        ));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'date_range',
          filterValue: '${event.startDate.toString().split(' ')[0]} - ${event.endDate.toString().split(' ')[0]}',
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to load certificates by date range: ${e.toString()}',
        failedOperation: 'load_by_date_range',
      ));
    }
  }

  /// Handle creating a new certificate
  Future<void> _onCreateCertificate(
    CreateCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'create',
        message: 'Creating certificate...',
      ));
      
      final createdCertificate = await _certificateRepository.createCertificate(event.certificate);
      
      // Reload all certificates to get updated list
      final allCertificates = await _certificateRepository.getAllCertificates();
      
      emit(CertificateOperationSuccess(
        operationType: 'create',
        message: 'Certificate created successfully',
        certificates: allCertificates,
        operatedCertificate: createdCertificate,
      ));
      
      // Transition to loaded state
      emit(CertificateLoaded(
        certificates: allCertificates,
        selectedCertificate: createdCertificate,
      ));
      
    } catch (e) {
      final currentCertificates = state is CertificateLoaded 
          ? (state as CertificateLoaded).certificates 
          : <Certificate>[];
      
      emit(CertificateError(
        message: 'Failed to create certificate: ${e.toString()}',
        certificates: currentCertificates,
        failedOperation: 'create',
      ));
    }
  }

  /// Handle updating an existing certificate
  Future<void> _onUpdateCertificate(
    UpdateCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'update',
        message: 'Updating certificate...',
      ));
      
      final updatedCertificate = await _certificateRepository.updateCertificate(event.certificate);
      
      // Reload all certificates to get updated list
      final allCertificates = await _certificateRepository.getAllCertificates();
      
      emit(CertificateOperationSuccess(
        operationType: 'update',
        message: 'Certificate updated successfully',
        certificates: allCertificates,
        operatedCertificate: updatedCertificate,
      ));
      
      // Transition to loaded state
      emit(CertificateLoaded(
        certificates: allCertificates,
        selectedCertificate: updatedCertificate,
      ));
      
    } catch (e) {
      final currentCertificates = state is CertificateLoaded 
          ? (state as CertificateLoaded).certificates 
          : <Certificate>[];
      
      emit(CertificateError(
        message: 'Failed to update certificate: ${e.toString()}',
        certificates: currentCertificates,
        failedOperation: 'update',
      ));
    }
  }

  /// Handle deleting a certificate
  Future<void> _onDeleteCertificate(
    DeleteCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'delete',
        message: 'Deleting certificate...',
      ));
      
      await _certificateRepository.deleteCertificate(event.certificateId);
      
      // Reload all certificates to get updated list
      final allCertificates = await _certificateRepository.getAllCertificates();
      
      emit(CertificateOperationSuccess(
        operationType: 'delete',
        message: 'Certificate deleted successfully',
        certificates: allCertificates,
      ));
      
      // Transition to loaded state
      if (allCertificates.isEmpty) {
        emit(const CertificateEmpty(message: 'No certificates found'));
      } else {
        emit(CertificateLoaded(certificates: allCertificates));
      }
      
    } catch (e) {
      final currentCertificates = state is CertificateLoaded 
          ? (state as CertificateLoaded).certificates 
          : <Certificate>[];
      
      emit(CertificateError(
        message: 'Failed to delete certificate: ${e.toString()}',
        certificates: currentCertificates,
        failedOperation: 'delete',
      ));
    }
  }

  /// Handle searching certificates
  Future<void> _onSearchCertificates(
    SearchCertificates event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateLoading());
      
      final certificates = await _certificateRepository.searchCertificates(event.searchTerm);
      
      if (certificates.isEmpty) {
        emit(CertificateEmpty(
          message: 'No certificates found matching "${event.searchTerm}"',
          criteria: event.searchTerm,
        ));
      } else {
        emit(CertificateLoaded(
          certificates: certificates,
          filterType: 'search',
          searchTerm: event.searchTerm,
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to search certificates: ${e.toString()}',
        failedOperation: 'search',
      ));
    }
  }

  /// Handle verifying a certificate
  Future<void> _onVerifyCertificate(
    VerifyCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      final verificationResult = await _certificateRepository.verifyCertificate(event.certificateNumber);
      
      emit(CertificateVerified(
        certificate: verificationResult['certificate'],
        isVerified: verificationResult['isVerified'],
        verificationMessage: verificationResult['message'],
        verifiedAt: DateTime.now(),
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to verify certificate: ${e.toString()}',
        failedOperation: 'verify',
      ));
    }
  }

  /// Handle generating certificate PDF
  Future<void> _onGenerateCertificatePDF(
    GenerateCertificatePDF event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'generate_pdf',
        message: 'Generating PDF...',
      ));
      
      final result = await _certificateRepository.generateCertificatePDF(event.certificateId);
      
      emit(CertificatePDFGenerated(
        certificate: result['certificate'],
        filePath: result['filePath'],
        message: 'PDF generated successfully',
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to generate PDF: ${e.toString()}',
        failedOperation: 'generate_pdf',
      ));
    }
  }

  /// Handle updating certificate status
  Future<void> _onUpdateCertificateStatus(
    UpdateCertificateStatus event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'update_status',
        message: 'Updating certificate status...',
      ));
      
      final updatedCertificate = await _certificateRepository.updateCertificateStatus(
        event.certificateId,
        event.status,
      );
      
      // Reload all certificates to get updated list
      final allCertificates = await _certificateRepository.getAllCertificates();
      
      emit(CertificateOperationSuccess(
        operationType: 'update_status',
        message: 'Certificate status updated successfully',
        certificates: allCertificates,
        operatedCertificate: updatedCertificate,
      ));
      
      // Transition to loaded state
      emit(CertificateLoaded(
        certificates: allCertificates,
        selectedCertificate: updatedCertificate,
      ));
      
    } catch (e) {
      final currentCertificates = state is CertificateLoaded 
          ? (state as CertificateLoaded).certificates 
          : <Certificate>[];
      
      emit(CertificateError(
        message: 'Failed to update certificate status: ${e.toString()}',
        certificates: currentCertificates,
        failedOperation: 'update_status',
      ));
    }
  }

  /// Handle selecting a certificate
  Future<void> _onSelectCertificate(
    SelectCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    if (state is CertificateLoaded) {
      final currentState = state as CertificateLoaded;
      emit(currentState.copyWith(
        selectedCertificate: event.certificate,
      ));
    }
  }

  /// Handle clearing certificate data
  Future<void> _onClearCertificates(
    ClearCertificates event,
    Emitter<CertificateState> emit,
  ) async {
    emit(const CertificateCleared());
  }

  /// Handle refreshing certificate data
  Future<void> _onRefreshCertificates(
    RefreshCertificates event,
    Emitter<CertificateState> emit,
  ) async {
    if (state is CertificateLoaded) {
      final currentState = state as CertificateLoaded;
      emit(currentState.copyWith(isRefreshing: true));
      
      try {
        final certificates = await _certificateRepository.getAllCertificates();
        
        emit(currentState.copyWith(
          certificates: certificates,
          isRefreshing: false,
        ));
      } catch (e) {
        emit(CertificateError(
          message: 'Failed to refresh certificates: ${e.toString()}',
          certificates: currentState.certificates,
          failedOperation: 'refresh',
        ));
      }
    } else {
      // If not in loaded state, perform regular load
      add(const LoadCertificates());
    }
  }

  /// Handle validating a certificate
  Future<void> _onValidateCertificate(
    ValidateCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    emit(CertificateValidating(
      certificate: event.certificate,
      validationMessage: 'Validating certificate...',
    ));
    
    try {
      final validationResult = await _certificateRepository.validateCertificate(event.certificate);
      
      emit(CertificateValidated(
        certificate: event.certificate,
        isValid: validationResult['isValid'] ?? false,
        validationErrors: List<String>.from(validationResult['errors'] ?? []),
        validationWarnings: List<String>.from(validationResult['warnings'] ?? []),
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to validate certificate: ${e.toString()}',
        failedOperation: 'validate',
      ));
    }
  }

  /// Handle issuing a certificate
  Future<void> _onIssueCertificate(
    IssueCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      final issuedCertificate = await _certificateRepository.issueCertificate(event.certificateId);
      
      emit(CertificateIssued(
        certificate: issuedCertificate,
        message: 'Certificate issued successfully',
        issuedAt: DateTime.now(),
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to issue certificate: ${e.toString()}',
        failedOperation: 'issue',
      ));
    }
  }

  /// Handle revoking a certificate
  Future<void> _onRevokeCertificate(
    RevokeCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      final revokedCertificate = await _certificateRepository.revokeCertificate(
        event.certificateId,
        event.reason,
      );
      
      emit(CertificateRevoked(
        certificate: revokedCertificate,
        reason: event.reason,
        revokedAt: DateTime.now(),
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to revoke certificate: ${e.toString()}',
        failedOperation: 'revoke',
      ));
    }
  }

  /// Handle duplicating a certificate
  Future<void> _onDuplicateCertificate(
    DuplicateCertificate event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'duplicate',
        message: 'Duplicating certificate...',
      ));
      
      final duplicatedCertificate = await _certificateRepository.duplicateCertificate(
        event.certificateId,
        event.newStudentId,
      );
      
      // Reload all certificates to get updated list
      final allCertificates = await _certificateRepository.getAllCertificates();
      
      emit(CertificateOperationSuccess(
        operationType: 'duplicate',
        message: 'Certificate duplicated successfully',
        certificates: allCertificates,
        operatedCertificate: duplicatedCertificate,
      ));
      
      // Transition to loaded state
      emit(CertificateLoaded(
        certificates: allCertificates,
        selectedCertificate: duplicatedCertificate,
      ));
      
    } catch (e) {
      final currentCertificates = state is CertificateLoaded 
          ? (state as CertificateLoaded).certificates 
          : <Certificate>[];
      
      emit(CertificateError(
        message: 'Failed to duplicate certificate: ${e.toString()}',
        certificates: currentCertificates,
        failedOperation: 'duplicate',
      ));
    }
  }

  /// Handle bulk generating certificates
  Future<void> _onBulkGenerateCertificates(
    BulkGenerateCertificates event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      final totalCount = event.studentIds.length;
      emit(BulkCertificateGenerating(
        totalCount: totalCount,
        completedCount: 0,
      ));
      
      final result = await _certificateRepository.bulkGenerateCertificates(
        courseId: event.courseId,
        studentIds: event.studentIds,
        type: event.type,
        template: event.template,
        onProgress: (completed, currentStudentName) {
          emit(BulkCertificateGenerating(
            totalCount: totalCount,
            completedCount: completed,
            currentStudentName: currentStudentName,
          ));
        },
      );
      
      emit(BulkCertificateGenerated(
        generatedCertificates: result['certificates'],
        successCount: result['successCount'],
        failureCount: result['failureCount'],
        errors: List<String>.from(result['errors'] ?? []),
      ));
      
      // Reload all certificates
      final allCertificates = await _certificateRepository.getAllCertificates();
      emit(CertificateLoaded(certificates: allCertificates));
      
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to bulk generate certificates: ${e.toString()}',
        failedOperation: 'bulk_generate',
      ));
    }
  }

  /// Handle exporting certificates
  Future<void> _onExportCertificates(
    ExportCertificates event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'export',
        message: 'Exporting certificates...',
      ));
      
      final result = await _certificateRepository.exportCertificates(
        event.certificateIds,
        event.format,
      );
      
      emit(CertificatesExported(
        filePath: result['filePath'],
        format: event.format,
        certificateCount: event.certificateIds.length,
        message: 'Certificates exported successfully',
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to export certificates: ${e.toString()}',
        failedOperation: 'export',
      ));
    }
  }

  /// Handle sending certificate email
  Future<void> _onSendCertificateEmail(
    SendCertificateEmail event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      emit(const CertificateOperationInProgress(
        operationType: 'send_email',
        message: 'Sending certificate email...',
      ));
      
      final result = await _certificateRepository.sendCertificateEmail(
        event.certificateId,
        event.customMessage,
      );
      
      emit(CertificateEmailSent(
        certificate: result['certificate'],
        recipientEmail: result['recipientEmail'],
        message: 'Certificate email sent successfully',
        sentAt: DateTime.now(),
      ));
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to send certificate email: ${e.toString()}',
        failedOperation: 'send_email',
      ));
    }
  }

  /// Handle getting certificate statistics
  Future<void> _onGetCertificateStatistics(
    GetCertificateStatistics event,
    Emitter<CertificateState> emit,
  ) async {
    try {
      final statistics = await _certificateRepository.getCertificateStatistics(
        startDate: event.startDate,
        endDate: event.endDate,
      );
      
      if (state is CertificateLoaded) {
        final currentState = state as CertificateLoaded;
        emit(currentState.copyWith(statistics: statistics));
      } else {
        // Load certificates with statistics
        final certificates = await _certificateRepository.getAllCertificates();
        emit(CertificateLoaded(
          certificates: certificates,
          statistics: statistics,
        ));
      }
    } catch (e) {
      emit(CertificateError(
        message: 'Failed to get certificate statistics: ${e.toString()}',
        failedOperation: 'get_statistics',
      ));
    }
  }
}
