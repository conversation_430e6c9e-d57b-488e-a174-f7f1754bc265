import 'package:equatable/equatable.dart';
import '../../models/certificate_model.dart';

/// Abstract base class for all certificate-related events
abstract class CertificateEvent extends Equatable {
  const CertificateEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all certificates
class LoadCertificates extends CertificateEvent {
  const LoadCertificates();

  @override
  String toString() => 'LoadCertificates';
}

/// Event to load certificates by student ID
class LoadCertificatesByStudent extends CertificateEvent {
  final String studentId;

  const LoadCertificatesByStudent(this.studentId);

  @override
  List<Object?> get props => [studentId];

  @override
  String toString() => 'LoadCertificatesByStudent(studentId: $studentId)';
}

/// Event to load certificates by course ID
class LoadCertificatesByCourse extends CertificateEvent {
  final String courseId;

  const LoadCertificatesByCourse(this.courseId);

  @override
  List<Object?> get props => [courseId];

  @override
  String toString() => 'LoadCertificatesByCourse(courseId: $courseId)';
}

/// Event to load certificates by type
class LoadCertificatesByType extends CertificateEvent {
  final CertificateType type;

  const LoadCertificatesByType(this.type);

  @override
  List<Object?> get props => [type];

  @override
  String toString() => 'LoadCertificatesByType(type: ${type.name})';
}

/// Event to load certificates by status
class LoadCertificatesByStatus extends CertificateEvent {
  final CertificateStatus status;

  const LoadCertificatesByStatus(this.status);

  @override
  List<Object?> get props => [status];

  @override
  String toString() => 'LoadCertificatesByStatus(status: ${status.name})';
}

/// Event to load certificates by date range
class LoadCertificatesByDateRange extends CertificateEvent {
  final DateTime startDate;
  final DateTime endDate;

  const LoadCertificatesByDateRange({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];

  @override
  String toString() => 'LoadCertificatesByDateRange(startDate: $startDate, endDate: $endDate)';
}

/// Event to create a new certificate
class CreateCertificate extends CertificateEvent {
  final Certificate certificate;

  const CreateCertificate(this.certificate);

  @override
  List<Object?> get props => [certificate];

  @override
  String toString() => 'CreateCertificate(certificate: ${certificate.certificateNumber})';
}

/// Event to update an existing certificate
class UpdateCertificate extends CertificateEvent {
  final Certificate certificate;

  const UpdateCertificate(this.certificate);

  @override
  List<Object?> get props => [certificate];

  @override
  String toString() => 'UpdateCertificate(certificate: ${certificate.certificateNumber})';
}

/// Event to delete a certificate
class DeleteCertificate extends CertificateEvent {
  final String certificateId;

  const DeleteCertificate(this.certificateId);

  @override
  List<Object?> get props => [certificateId];

  @override
  String toString() => 'DeleteCertificate(certificateId: $certificateId)';
}

/// Event to search certificates
class SearchCertificates extends CertificateEvent {
  final String searchTerm;

  const SearchCertificates(this.searchTerm);

  @override
  List<Object?> get props => [searchTerm];

  @override
  String toString() => 'SearchCertificates(searchTerm: $searchTerm)';
}

/// Event to verify a certificate
class VerifyCertificate extends CertificateEvent {
  final String certificateNumber;

  const VerifyCertificate(this.certificateNumber);

  @override
  List<Object?> get props => [certificateNumber];

  @override
  String toString() => 'VerifyCertificate(certificateNumber: $certificateNumber)';
}

/// Event to generate certificate PDF
class GenerateCertificatePDF extends CertificateEvent {
  final String certificateId;

  const GenerateCertificatePDF(this.certificateId);

  @override
  List<Object?> get props => [certificateId];

  @override
  String toString() => 'GenerateCertificatePDF(certificateId: $certificateId)';
}

/// Event to update certificate status
class UpdateCertificateStatus extends CertificateEvent {
  final String certificateId;
  final CertificateStatus status;

  const UpdateCertificateStatus({
    required this.certificateId,
    required this.status,
  });

  @override
  List<Object?> get props => [certificateId, status];

  @override
  String toString() => 'UpdateCertificateStatus(certificateId: $certificateId, status: ${status.name})';
}

/// Event to select a certificate
class SelectCertificate extends CertificateEvent {
  final Certificate? certificate;

  const SelectCertificate(this.certificate);

  @override
  List<Object?> get props => [certificate];

  @override
  String toString() => 'SelectCertificate(certificate: ${certificate?.certificateNumber})';
}

/// Event to clear certificate data
class ClearCertificates extends CertificateEvent {
  const ClearCertificates();

  @override
  String toString() => 'ClearCertificates';
}

/// Event to refresh certificate data
class RefreshCertificates extends CertificateEvent {
  const RefreshCertificates();

  @override
  String toString() => 'RefreshCertificates';
}

/// Event to validate certificate data
class ValidateCertificate extends CertificateEvent {
  final Certificate certificate;

  const ValidateCertificate(this.certificate);

  @override
  List<Object?> get props => [certificate];

  @override
  String toString() => 'ValidateCertificate(certificate: ${certificate.certificateNumber})';
}

/// Event to issue a certificate
class IssueCertificate extends CertificateEvent {
  final String certificateId;

  const IssueCertificate(this.certificateId);

  @override
  List<Object?> get props => [certificateId];

  @override
  String toString() => 'IssueCertificate(certificateId: $certificateId)';
}

/// Event to revoke a certificate
class RevokeCertificate extends CertificateEvent {
  final String certificateId;
  final String reason;

  const RevokeCertificate({
    required this.certificateId,
    required this.reason,
  });

  @override
  List<Object?> get props => [certificateId, reason];

  @override
  String toString() => 'RevokeCertificate(certificateId: $certificateId, reason: $reason)';
}

/// Event to duplicate a certificate
class DuplicateCertificate extends CertificateEvent {
  final String certificateId;
  final String newStudentId;

  const DuplicateCertificate({
    required this.certificateId,
    required this.newStudentId,
  });

  @override
  List<Object?> get props => [certificateId, newStudentId];

  @override
  String toString() => 'DuplicateCertificate(certificateId: $certificateId, newStudentId: $newStudentId)';
}

/// Event to bulk generate certificates
class BulkGenerateCertificates extends CertificateEvent {
  final String courseId;
  final List<String> studentIds;
  final CertificateType type;
  final CertificateTemplate template;

  const BulkGenerateCertificates({
    required this.courseId,
    required this.studentIds,
    required this.type,
    required this.template,
  });

  @override
  List<Object?> get props => [courseId, studentIds, type, template];

  @override
  String toString() => 'BulkGenerateCertificates('
      'courseId: $courseId, '
      'studentIds: ${studentIds.length}, '
      'type: ${type.name}, '
      'template: ${template.name})';
}

/// Event to export certificates
class ExportCertificates extends CertificateEvent {
  final List<String> certificateIds;
  final String format; // 'pdf', 'excel', 'csv'

  const ExportCertificates({
    required this.certificateIds,
    required this.format,
  });

  @override
  List<Object?> get props => [certificateIds, format];

  @override
  String toString() => 'ExportCertificates(certificateIds: ${certificateIds.length}, format: $format)';
}

/// Event to send certificate via email
class SendCertificateEmail extends CertificateEvent {
  final String certificateId;
  final String? customMessage;

  const SendCertificateEmail({
    required this.certificateId,
    this.customMessage,
  });

  @override
  List<Object?> get props => [certificateId, customMessage];

  @override
  String toString() => 'SendCertificateEmail(certificateId: $certificateId)';
}

/// Event to get certificate statistics
class GetCertificateStatistics extends CertificateEvent {
  final DateTime? startDate;
  final DateTime? endDate;

  const GetCertificateStatistics({
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];

  @override
  String toString() => 'GetCertificateStatistics(startDate: $startDate, endDate: $endDate)';
}
