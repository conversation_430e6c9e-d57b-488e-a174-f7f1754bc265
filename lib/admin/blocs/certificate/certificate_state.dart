import 'package:equatable/equatable.dart';
import '../../models/certificate_model.dart';

/// Abstract base class for all certificate-related states
abstract class CertificateState extends Equatable {
  const CertificateState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the CertificateBloc
class CertificateInitial extends CertificateState {
  const CertificateInitial();

  @override
  String toString() => 'CertificateInitial';
}

/// State indicating that certificates are being loaded
class CertificateLoading extends CertificateState {
  const CertificateLoading();

  @override
  String toString() => 'CertificateLoading';
}

/// State indicating that certificates have been loaded successfully
class CertificateLoaded extends CertificateState {
  /// List of certificates
  final List<Certificate> certificates;
  
  /// Currently selected certificate (if any)
  final Certificate? selectedCertificate;
  
  /// Current filter type (e.g., "all", "student", "course", "type", "status")
  final String filterType;
  
  /// Current filter value (e.g., student ID, course ID, type, status)
  final String? filterValue;
  
  /// Current search term (if searching)
  final String? searchTerm;
  
  /// Whether the data is being refreshed
  final bool isRefreshing;
  
  /// Certificate statistics (if loaded)
  final Map<String, dynamic>? statistics;

  const CertificateLoaded({
    required this.certificates,
    this.selectedCertificate,
    this.filterType = 'all',
    this.filterValue,
    this.searchTerm,
    this.isRefreshing = false,
    this.statistics,
  });

  /// Create a copy of this state with updated values
  CertificateLoaded copyWith({
    List<Certificate>? certificates,
    Certificate? selectedCertificate,
    String? filterType,
    String? filterValue,
    String? searchTerm,
    bool? isRefreshing,
    Map<String, dynamic>? statistics,
    bool clearSelectedCertificate = false,
    bool clearFilterValue = false,
    bool clearSearchTerm = false,
    bool clearStatistics = false,
  }) {
    return CertificateLoaded(
      certificates: certificates ?? this.certificates,
      selectedCertificate: clearSelectedCertificate 
          ? null 
          : selectedCertificate ?? this.selectedCertificate,
      filterType: filterType ?? this.filterType,
      filterValue: clearFilterValue 
          ? null 
          : filterValue ?? this.filterValue,
      searchTerm: clearSearchTerm 
          ? null 
          : searchTerm ?? this.searchTerm,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      statistics: clearStatistics 
          ? null 
          : statistics ?? this.statistics,
    );
  }

  @override
  List<Object?> get props => [
        certificates,
        selectedCertificate,
        filterType,
        filterValue,
        searchTerm,
        isRefreshing,
        statistics,
      ];

  @override
  String toString() => 'CertificateLoaded('
      'certificates: ${certificates.length}, '
      'selectedCertificate: ${selectedCertificate?.certificateNumber}, '
      'filterType: $filterType, '
      'filterValue: $filterValue, '
      'searchTerm: $searchTerm, '
      'isRefreshing: $isRefreshing)';
}

/// State indicating that a certificate operation is in progress
class CertificateOperationInProgress extends CertificateState {
  /// The type of operation being performed
  final String operationType;
  
  /// Optional message describing the operation
  final String? message;
  
  /// Progress percentage (0.0 to 1.0) if applicable
  final double? progress;

  const CertificateOperationInProgress({
    required this.operationType,
    this.message,
    this.progress,
  });

  @override
  List<Object?> get props => [operationType, message, progress];

  @override
  String toString() => 'CertificateOperationInProgress('
      'operationType: $operationType, '
      'message: $message, '
      'progress: $progress)';
}

/// State indicating that a certificate operation was successful
class CertificateOperationSuccess extends CertificateState {
  /// The type of operation that was performed
  final String operationType;
  
  /// Success message
  final String message;
  
  /// Updated list of certificates
  final List<Certificate> certificates;
  
  /// The certificate that was operated on (if applicable)
  final Certificate? operatedCertificate;
  
  /// Additional data from the operation (e.g., file path for PDF generation)
  final Map<String, dynamic>? operationData;

  const CertificateOperationSuccess({
    required this.operationType,
    required this.message,
    required this.certificates,
    this.operatedCertificate,
    this.operationData,
  });

  @override
  List<Object?> get props => [
        operationType,
        message,
        certificates,
        operatedCertificate,
        operationData,
      ];

  @override
  String toString() => 'CertificateOperationSuccess('
      'operationType: $operationType, '
      'message: $message, '
      'certificates: ${certificates.length}, '
      'operatedCertificate: ${operatedCertificate?.certificateNumber})';
}

/// State indicating that an error occurred
class CertificateError extends CertificateState {
  /// Error message describing what went wrong
  final String message;
  
  /// Current list of certificates (maintained for UI consistency)
  final List<Certificate> certificates;
  
  /// The operation that failed
  final String? failedOperation;
  
  /// Error code if available
  final String? errorCode;

  const CertificateError({
    required this.message,
    this.certificates = const [],
    this.failedOperation,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, certificates, failedOperation, errorCode];

  @override
  String toString() => 'CertificateError('
      'message: $message, '
      'certificates: ${certificates.length}, '
      'failedOperation: $failedOperation, '
      'errorCode: $errorCode)';
}

/// State indicating that no certificates were found
class CertificateEmpty extends CertificateState {
  /// Message explaining why no certificates were found
  final String message;
  
  /// The filter or search criteria that resulted in no results
  final String? criteria;
  
  /// Suggestions for the user
  final List<String>? suggestions;

  const CertificateEmpty({
    required this.message,
    this.criteria,
    this.suggestions,
  });

  @override
  List<Object?> get props => [message, criteria, suggestions];

  @override
  String toString() => 'CertificateEmpty('
      'message: $message, '
      'criteria: $criteria, '
      'suggestions: $suggestions)';
}

/// State indicating that certificate data has been cleared
class CertificateCleared extends CertificateState {
  const CertificateCleared();

  @override
  String toString() => 'CertificateCleared';
}

/// State indicating that certificate validation is in progress
class CertificateValidating extends CertificateState {
  final Certificate certificate;
  final String? validationMessage;

  const CertificateValidating({
    required this.certificate,
    this.validationMessage,
  });

  @override
  List<Object?> get props => [certificate, validationMessage];

  @override
  String toString() => 'CertificateValidating('
      'certificate: ${certificate.certificateNumber}, '
      'validationMessage: $validationMessage)';
}

/// State indicating certificate validation results
class CertificateValidated extends CertificateState {
  final Certificate certificate;
  final bool isValid;
  final List<String> validationErrors;
  final List<String> validationWarnings;

  const CertificateValidated({
    required this.certificate,
    required this.isValid,
    this.validationErrors = const [],
    this.validationWarnings = const [],
  });

  @override
  List<Object?> get props => [
        certificate,
        isValid,
        validationErrors,
        validationWarnings,
      ];

  @override
  String toString() => 'CertificateValidated('
      'certificate: ${certificate.certificateNumber}, '
      'isValid: $isValid, '
      'errors: ${validationErrors.length}, '
      'warnings: ${validationWarnings.length})';
}

/// State indicating that a certificate has been verified
class CertificateVerified extends CertificateState {
  final Certificate certificate;
  final bool isVerified;
  final String verificationMessage;
  final DateTime verifiedAt;

  const CertificateVerified({
    required this.certificate,
    required this.isVerified,
    required this.verificationMessage,
    required this.verifiedAt,
  });

  @override
  List<Object?> get props => [
        certificate,
        isVerified,
        verificationMessage,
        verifiedAt,
      ];

  @override
  String toString() => 'CertificateVerified('
      'certificate: ${certificate.certificateNumber}, '
      'isVerified: $isVerified, '
      'message: $verificationMessage, '
      'verifiedAt: $verifiedAt)';
}

/// State indicating that a certificate has been issued
class CertificateIssued extends CertificateState {
  final Certificate certificate;
  final String message;
  final DateTime issuedAt;

  const CertificateIssued({
    required this.certificate,
    required this.message,
    required this.issuedAt,
  });

  @override
  List<Object?> get props => [certificate, message, issuedAt];

  @override
  String toString() => 'CertificateIssued('
      'certificate: ${certificate.certificateNumber}, '
      'message: $message, '
      'issuedAt: $issuedAt)';
}

/// State indicating that a certificate has been revoked
class CertificateRevoked extends CertificateState {
  final Certificate certificate;
  final String reason;
  final DateTime revokedAt;

  const CertificateRevoked({
    required this.certificate,
    required this.reason,
    required this.revokedAt,
  });

  @override
  List<Object?> get props => [certificate, reason, revokedAt];

  @override
  String toString() => 'CertificateRevoked('
      'certificate: ${certificate.certificateNumber}, '
      'reason: $reason, '
      'revokedAt: $revokedAt)';
}

/// State indicating that certificate PDF generation is complete
class CertificatePDFGenerated extends CertificateState {
  final Certificate certificate;
  final String filePath;
  final String message;

  const CertificatePDFGenerated({
    required this.certificate,
    required this.filePath,
    required this.message,
  });

  @override
  List<Object?> get props => [certificate, filePath, message];

  @override
  String toString() => 'CertificatePDFGenerated('
      'certificate: ${certificate.certificateNumber}, '
      'filePath: $filePath, '
      'message: $message)';
}

/// State indicating that bulk certificate generation is in progress
class BulkCertificateGenerating extends CertificateState {
  final int totalCount;
  final int completedCount;
  final String? currentStudentName;

  const BulkCertificateGenerating({
    required this.totalCount,
    required this.completedCount,
    this.currentStudentName,
  });

  double get progress => totalCount > 0 ? completedCount / totalCount : 0.0;

  @override
  List<Object?> get props => [totalCount, completedCount, currentStudentName];

  @override
  String toString() => 'BulkCertificateGenerating('
      'totalCount: $totalCount, '
      'completedCount: $completedCount, '
      'currentStudentName: $currentStudentName)';
}

/// State indicating that bulk certificate generation is complete
class BulkCertificateGenerated extends CertificateState {
  final List<Certificate> generatedCertificates;
  final int successCount;
  final int failureCount;
  final List<String> errors;

  const BulkCertificateGenerated({
    required this.generatedCertificates,
    required this.successCount,
    required this.failureCount,
    this.errors = const [],
  });

  @override
  List<Object?> get props => [
        generatedCertificates,
        successCount,
        failureCount,
        errors,
      ];

  @override
  String toString() => 'BulkCertificateGenerated('
      'generatedCertificates: ${generatedCertificates.length}, '
      'successCount: $successCount, '
      'failureCount: $failureCount, '
      'errors: ${errors.length})';
}

/// State indicating that certificates have been exported
class CertificatesExported extends CertificateState {
  final String filePath;
  final String format;
  final int certificateCount;
  final String message;

  const CertificatesExported({
    required this.filePath,
    required this.format,
    required this.certificateCount,
    required this.message,
  });

  @override
  List<Object?> get props => [filePath, format, certificateCount, message];

  @override
  String toString() => 'CertificatesExported('
      'filePath: $filePath, '
      'format: $format, '
      'certificateCount: $certificateCount, '
      'message: $message)';
}

/// State indicating that certificate email has been sent
class CertificateEmailSent extends CertificateState {
  final Certificate certificate;
  final String recipientEmail;
  final String message;
  final DateTime sentAt;

  const CertificateEmailSent({
    required this.certificate,
    required this.recipientEmail,
    required this.message,
    required this.sentAt,
  });

  @override
  List<Object?> get props => [certificate, recipientEmail, message, sentAt];

  @override
  String toString() => 'CertificateEmailSent('
      'certificate: ${certificate.certificateNumber}, '
      'recipientEmail: $recipientEmail, '
      'message: $message, '
      'sentAt: $sentAt)';
}
