import 'package:equatable/equatable.dart';
import '../../models/exam_model.dart';

/// Abstract base class for all exam-related events
abstract class ExamEvent extends Equatable {
  const ExamEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all exams
class LoadExams extends ExamEvent {
  const LoadExams();

  @override
  String toString() => 'LoadExams';
}

/// Event to load exams by course ID
class LoadExamsByCourse extends ExamEvent {
  final String courseId;

  const LoadExamsByCourse(this.courseId);

  @override
  List<Object?> get props => [courseId];

  @override
  String toString() => 'LoadExamsByCourse(courseId: $courseId)';
}

/// Event to load exams by instructor ID
class LoadExamsByInstructor extends ExamEvent {
  final String instructorId;

  const LoadExamsByInstructor(this.instructorId);

  @override
  List<Object?> get props => [instructorId];

  @override
  String toString() => 'LoadExamsByInstructor(instructorId: $instructorId)';
}

/// Event to load exams by status
class LoadExamsByStatus extends ExamEvent {
  final ExamStatus status;

  const LoadExamsByStatus(this.status);

  @override
  List<Object?> get props => [status];

  @override
  String toString() => 'LoadExamsByStatus(status: ${status.name})';
}

/// Event to load upcoming exams
class LoadUpcomingExams extends ExamEvent {
  const LoadUpcomingExams();

  @override
  String toString() => 'LoadUpcomingExams';
}

/// Event to load active exams
class LoadActiveExams extends ExamEvent {
  const LoadActiveExams();

  @override
  String toString() => 'LoadActiveExams';
}

/// Event to load exams by date range
class LoadExamsByDateRange extends ExamEvent {
  final DateTime startDate;
  final DateTime endDate;

  const LoadExamsByDateRange({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];

  @override
  String toString() => 'LoadExamsByDateRange(startDate: $startDate, endDate: $endDate)';
}

/// Event to create a new exam
class CreateExam extends ExamEvent {
  final Exam exam;

  const CreateExam(this.exam);

  @override
  List<Object?> get props => [exam];

  @override
  String toString() => 'CreateExam(exam: ${exam.title})';
}

/// Event to update an existing exam
class UpdateExam extends ExamEvent {
  final Exam exam;

  const UpdateExam(this.exam);

  @override
  List<Object?> get props => [exam];

  @override
  String toString() => 'UpdateExam(exam: ${exam.title})';
}

/// Event to delete an exam
class DeleteExam extends ExamEvent {
  final String examId;

  const DeleteExam(this.examId);

  @override
  List<Object?> get props => [examId];

  @override
  String toString() => 'DeleteExam(examId: $examId)';
}

/// Event to search exams
class SearchExams extends ExamEvent {
  final String searchTerm;

  const SearchExams(this.searchTerm);

  @override
  List<Object?> get props => [searchTerm];

  @override
  String toString() => 'SearchExams(searchTerm: $searchTerm)';
}

/// Event to publish an exam
class PublishExam extends ExamEvent {
  final String examId;

  const PublishExam(this.examId);

  @override
  List<Object?> get props => [examId];

  @override
  String toString() => 'PublishExam(examId: $examId)';
}

/// Event to update exam status
class UpdateExamStatus extends ExamEvent {
  final String examId;
  final ExamStatus status;

  const UpdateExamStatus({
    required this.examId,
    required this.status,
  });

  @override
  List<Object?> get props => [examId, status];

  @override
  String toString() => 'UpdateExamStatus(examId: $examId, status: ${status.name})';
}

/// Event to select an exam
class SelectExam extends ExamEvent {
  final Exam? exam;

  const SelectExam(this.exam);

  @override
  List<Object?> get props => [exam];

  @override
  String toString() => 'SelectExam(exam: ${exam?.title})';
}

/// Event to clear exam data
class ClearExams extends ExamEvent {
  const ClearExams();

  @override
  String toString() => 'ClearExams';
}

/// Event to refresh exam data
class RefreshExams extends ExamEvent {
  const RefreshExams();

  @override
  String toString() => 'RefreshExams';
}

/// Event to add question to exam
class AddQuestionToExam extends ExamEvent {
  final String examId;
  final ExamQuestion question;

  const AddQuestionToExam({
    required this.examId,
    required this.question,
  });

  @override
  List<Object?> get props => [examId, question];

  @override
  String toString() => 'AddQuestionToExam(examId: $examId, question: ${question.question})';
}

/// Event to remove question from exam
class RemoveQuestionFromExam extends ExamEvent {
  final String examId;
  final String questionId;

  const RemoveQuestionFromExam({
    required this.examId,
    required this.questionId,
  });

  @override
  List<Object?> get props => [examId, questionId];

  @override
  String toString() => 'RemoveQuestionFromExam(examId: $examId, questionId: $questionId)';
}

/// Event to update question in exam
class UpdateQuestionInExam extends ExamEvent {
  final String examId;
  final ExamQuestion question;

  const UpdateQuestionInExam({
    required this.examId,
    required this.question,
  });

  @override
  List<Object?> get props => [examId, question];

  @override
  String toString() => 'UpdateQuestionInExam(examId: $examId, question: ${question.question})';
}

/// Event to validate exam data
class ValidateExam extends ExamEvent {
  final Exam exam;

  const ValidateExam(this.exam);

  @override
  List<Object?> get props => [exam];

  @override
  String toString() => 'ValidateExam(exam: ${exam.title})';
}

/// Event to duplicate an exam
class DuplicateExam extends ExamEvent {
  final String examId;
  final String newTitle;

  const DuplicateExam({
    required this.examId,
    required this.newTitle,
  });

  @override
  List<Object?> get props => [examId, newTitle];

  @override
  String toString() => 'DuplicateExam(examId: $examId, newTitle: $newTitle)';
}

/// Event to start an exam
class StartExam extends ExamEvent {
  final String examId;

  const StartExam(this.examId);

  @override
  List<Object?> get props => [examId];

  @override
  String toString() => 'StartExam(examId: $examId)';
}

/// Event to end an exam
class EndExam extends ExamEvent {
  final String examId;

  const EndExam(this.examId);

  @override
  List<Object?> get props => [examId];

  @override
  String toString() => 'EndExam(examId: $examId)';
}
