import 'package:flutter_bloc/flutter_bloc.dart';
import '../../repositories/exam_repository.dart';
import '../../models/exam_model.dart';
import 'exam_event.dart';
import 'exam_state.dart';

/// BLoC for managing exam operations
/// 
/// This BLoC handles all exam related operations including:
/// - Loading exams from Firebase
/// - Creating, updating, and deleting exams
/// - Filtering and searching exams
/// - Managing exam state across the application
/// - Publishing and managing exam lifecycle
/// 
/// Uses the Repository pattern to interact with Firebase through ExamRepository.
class ExamBloc extends Bloc<ExamEvent, ExamState> {
  /// Repository for exam operations
  final ExamRepository _examRepository;

  /// Constructor that initializes the BLoC with ExamInitial state
  ExamBloc({required ExamRepository examRepository})
      : _examRepository = examRepository,
        super(const ExamInitial()) {
    
    // Register event handlers
    on<LoadExams>(_onLoadExams);
    on<LoadExamsByCourse>(_onLoadExamsByCourse);
    on<LoadExamsByInstructor>(_onLoadExamsByInstructor);
    on<LoadExamsByStatus>(_onLoadExamsByStatus);
    on<LoadUpcomingExams>(_onLoadUpcomingExams);
    on<LoadActiveExams>(_onLoadActiveExams);
    on<LoadExamsByDateRange>(_onLoadExamsByDateRange);
    on<CreateExam>(_onCreateExam);
    on<UpdateExam>(_onUpdateExam);
    on<DeleteExam>(_onDeleteExam);
    on<SearchExams>(_onSearchExams);
    on<PublishExam>(_onPublishExam);
    on<UpdateExamStatus>(_onUpdateExamStatus);
    on<SelectExam>(_onSelectExam);
    on<ClearExams>(_onClearExams);
    on<RefreshExams>(_onRefreshExams);
    on<ValidateExam>(_onValidateExam);
    on<DuplicateExam>(_onDuplicateExam);
    on<StartExam>(_onStartExam);
    on<EndExam>(_onEndExam);
  }

  /// Handle loading all exams
  Future<void> _onLoadExams(
    LoadExams event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getAllExams();
      
      if (exams.isEmpty) {
        emit(const ExamEmpty(message: 'No exams found'));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'all',
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load exams: ${e.toString()}',
        failedOperation: 'load_all',
      ));
    }
  }

  /// Handle loading exams by course
  Future<void> _onLoadExamsByCourse(
    LoadExamsByCourse event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getExamsByCourse(event.courseId);
      
      if (exams.isEmpty) {
        emit(ExamEmpty(
          message: 'No exams found for this course',
          criteria: event.courseId,
        ));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'course',
          filterValue: event.courseId,
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load exams by course: ${e.toString()}',
        failedOperation: 'load_by_course',
      ));
    }
  }

  /// Handle loading exams by instructor
  Future<void> _onLoadExamsByInstructor(
    LoadExamsByInstructor event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getExamsByInstructor(event.instructorId);
      
      if (exams.isEmpty) {
        emit(ExamEmpty(
          message: 'No exams found for this instructor',
          criteria: event.instructorId,
        ));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'instructor',
          filterValue: event.instructorId,
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load exams by instructor: ${e.toString()}',
        failedOperation: 'load_by_instructor',
      ));
    }
  }

  /// Handle loading exams by status
  Future<void> _onLoadExamsByStatus(
    LoadExamsByStatus event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getExamsByStatus(event.status);
      
      if (exams.isEmpty) {
        emit(ExamEmpty(
          message: 'No exams found with status: ${event.status.name}',
          criteria: event.status.name,
        ));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'status',
          filterValue: event.status.name,
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load exams by status: ${e.toString()}',
        failedOperation: 'load_by_status',
      ));
    }
  }

  /// Handle loading upcoming exams
  Future<void> _onLoadUpcomingExams(
    LoadUpcomingExams event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getUpcomingExams();
      
      if (exams.isEmpty) {
        emit(const ExamEmpty(message: 'No upcoming exams found'));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'upcoming',
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load upcoming exams: ${e.toString()}',
        failedOperation: 'load_upcoming',
      ));
    }
  }

  /// Handle loading active exams
  Future<void> _onLoadActiveExams(
    LoadActiveExams event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getActiveExams();
      
      if (exams.isEmpty) {
        emit(const ExamEmpty(message: 'No active exams found'));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'active',
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load active exams: ${e.toString()}',
        failedOperation: 'load_active',
      ));
    }
  }

  /// Handle loading exams by date range
  Future<void> _onLoadExamsByDateRange(
    LoadExamsByDateRange event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.getExamsByDateRange(
        event.startDate,
        event.endDate,
      );
      
      if (exams.isEmpty) {
        emit(ExamEmpty(
          message: 'No exams found in the selected date range',
          criteria: '${event.startDate.toString().split(' ')[0]} - ${event.endDate.toString().split(' ')[0]}',
        ));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'date_range',
          filterValue: '${event.startDate.toString().split(' ')[0]} - ${event.endDate.toString().split(' ')[0]}',
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to load exams by date range: ${e.toString()}',
        failedOperation: 'load_by_date_range',
      ));
    }
  }

  /// Handle creating a new exam
  Future<void> _onCreateExam(
    CreateExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamOperationInProgress(
        operationType: 'create',
        message: 'Creating exam...',
      ));
      
      final createdExam = await _examRepository.createExam(event.exam);
      
      // Reload all exams to get updated list
      final allExams = await _examRepository.getAllExams();
      
      emit(ExamOperationSuccess(
        operationType: 'create',
        message: 'Exam created successfully',
        exams: allExams,
        operatedExam: createdExam,
      ));
      
      // Transition to loaded state
      emit(ExamLoaded(
        exams: allExams,
        selectedExam: createdExam,
      ));
      
    } catch (e) {
      final currentExams = state is ExamLoaded 
          ? (state as ExamLoaded).exams 
          : <Exam>[];
      
      emit(ExamError(
        message: 'Failed to create exam: ${e.toString()}',
        exams: currentExams,
        failedOperation: 'create',
      ));
    }
  }

  /// Handle updating an existing exam
  Future<void> _onUpdateExam(
    UpdateExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamOperationInProgress(
        operationType: 'update',
        message: 'Updating exam...',
      ));
      
      final updatedExam = await _examRepository.updateExam(event.exam);
      
      // Reload all exams to get updated list
      final allExams = await _examRepository.getAllExams();
      
      emit(ExamOperationSuccess(
        operationType: 'update',
        message: 'Exam updated successfully',
        exams: allExams,
        operatedExam: updatedExam,
      ));
      
      // Transition to loaded state
      emit(ExamLoaded(
        exams: allExams,
        selectedExam: updatedExam,
      ));
      
    } catch (e) {
      final currentExams = state is ExamLoaded 
          ? (state as ExamLoaded).exams 
          : <Exam>[];
      
      emit(ExamError(
        message: 'Failed to update exam: ${e.toString()}',
        exams: currentExams,
        failedOperation: 'update',
      ));
    }
  }

  /// Handle deleting an exam
  Future<void> _onDeleteExam(
    DeleteExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamOperationInProgress(
        operationType: 'delete',
        message: 'Deleting exam...',
      ));
      
      await _examRepository.deleteExam(event.examId);
      
      // Reload all exams to get updated list
      final allExams = await _examRepository.getAllExams();
      
      emit(ExamOperationSuccess(
        operationType: 'delete',
        message: 'Exam deleted successfully',
        exams: allExams,
      ));
      
      // Transition to loaded state
      if (allExams.isEmpty) {
        emit(const ExamEmpty(message: 'No exams found'));
      } else {
        emit(ExamLoaded(exams: allExams));
      }
      
    } catch (e) {
      final currentExams = state is ExamLoaded 
          ? (state as ExamLoaded).exams 
          : <Exam>[];
      
      emit(ExamError(
        message: 'Failed to delete exam: ${e.toString()}',
        exams: currentExams,
        failedOperation: 'delete',
      ));
    }
  }

  /// Handle searching exams
  Future<void> _onSearchExams(
    SearchExams event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamLoading());
      
      final exams = await _examRepository.searchExams(event.searchTerm);
      
      if (exams.isEmpty) {
        emit(ExamEmpty(
          message: 'No exams found matching "${event.searchTerm}"',
          criteria: event.searchTerm,
        ));
      } else {
        emit(ExamLoaded(
          exams: exams,
          filterType: 'search',
          searchTerm: event.searchTerm,
        ));
      }
    } catch (e) {
      emit(ExamError(
        message: 'Failed to search exams: ${e.toString()}',
        failedOperation: 'search',
      ));
    }
  }

  /// Handle publishing an exam
  Future<void> _onPublishExam(
    PublishExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamOperationInProgress(
        operationType: 'publish',
        message: 'Publishing exam...',
      ));
      
      final publishedExam = await _examRepository.publishExam(event.examId);
      
      emit(ExamPublished(
        exam: publishedExam,
        message: 'Exam published successfully',
        publishedAt: DateTime.now(),
      ));
      
      // Reload all exams to get updated list
      final allExams = await _examRepository.getAllExams();
      emit(ExamLoaded(
        exams: allExams,
        selectedExam: publishedExam,
      ));
      
    } catch (e) {
      final currentExams = state is ExamLoaded 
          ? (state as ExamLoaded).exams 
          : <Exam>[];
      
      emit(ExamError(
        message: 'Failed to publish exam: ${e.toString()}',
        exams: currentExams,
        failedOperation: 'publish',
      ));
    }
  }

  /// Handle updating exam status
  Future<void> _onUpdateExamStatus(
    UpdateExamStatus event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamOperationInProgress(
        operationType: 'update_status',
        message: 'Updating exam status...',
      ));
      
      final updatedExam = await _examRepository.updateExamStatus(
        event.examId,
        event.status,
      );
      
      // Reload all exams to get updated list
      final allExams = await _examRepository.getAllExams();
      
      emit(ExamOperationSuccess(
        operationType: 'update_status',
        message: 'Exam status updated successfully',
        exams: allExams,
        operatedExam: updatedExam,
      ));
      
      // Transition to loaded state
      emit(ExamLoaded(
        exams: allExams,
        selectedExam: updatedExam,
      ));
      
    } catch (e) {
      final currentExams = state is ExamLoaded 
          ? (state as ExamLoaded).exams 
          : <Exam>[];
      
      emit(ExamError(
        message: 'Failed to update exam status: ${e.toString()}',
        exams: currentExams,
        failedOperation: 'update_status',
      ));
    }
  }

  /// Handle selecting an exam
  Future<void> _onSelectExam(
    SelectExam event,
    Emitter<ExamState> emit,
  ) async {
    if (state is ExamLoaded) {
      final currentState = state as ExamLoaded;
      emit(currentState.copyWith(
        selectedExam: event.exam,
      ));
    }
  }

  /// Handle clearing exam data
  Future<void> _onClearExams(
    ClearExams event,
    Emitter<ExamState> emit,
  ) async {
    emit(const ExamCleared());
  }

  /// Handle refreshing exam data
  Future<void> _onRefreshExams(
    RefreshExams event,
    Emitter<ExamState> emit,
  ) async {
    if (state is ExamLoaded) {
      final currentState = state as ExamLoaded;
      emit(currentState.copyWith(isRefreshing: true));
      
      try {
        final exams = await _examRepository.getAllExams();
        
        emit(currentState.copyWith(
          exams: exams,
          isRefreshing: false,
        ));
      } catch (e) {
        emit(ExamError(
          message: 'Failed to refresh exams: ${e.toString()}',
          exams: currentState.exams,
          failedOperation: 'refresh',
        ));
      }
    } else {
      // If not in loaded state, perform regular load
      add(const LoadExams());
    }
  }

  /// Handle validating an exam
  Future<void> _onValidateExam(
    ValidateExam event,
    Emitter<ExamState> emit,
  ) async {
    emit(ExamValidating(
      exam: event.exam,
      validationMessage: 'Validating exam...',
    ));
    
    try {
      final validationResult = await _examRepository.validateExam(event.exam);
      
      emit(ExamValidated(
        exam: event.exam,
        isValid: validationResult['isValid'] ?? false,
        validationErrors: List<String>.from(validationResult['errors'] ?? []),
        validationWarnings: List<String>.from(validationResult['warnings'] ?? []),
      ));
    } catch (e) {
      emit(ExamError(
        message: 'Failed to validate exam: ${e.toString()}',
        failedOperation: 'validate',
      ));
    }
  }

  /// Handle duplicating an exam
  Future<void> _onDuplicateExam(
    DuplicateExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      emit(const ExamOperationInProgress(
        operationType: 'duplicate',
        message: 'Duplicating exam...',
      ));
      
      final duplicatedExam = await _examRepository.duplicateExam(
        event.examId,
        event.newTitle,
      );
      
      // Reload all exams to get updated list
      final allExams = await _examRepository.getAllExams();
      
      emit(ExamOperationSuccess(
        operationType: 'duplicate',
        message: 'Exam duplicated successfully',
        exams: allExams,
        operatedExam: duplicatedExam,
      ));
      
      // Transition to loaded state
      emit(ExamLoaded(
        exams: allExams,
        selectedExam: duplicatedExam,
      ));
      
    } catch (e) {
      final currentExams = state is ExamLoaded 
          ? (state as ExamLoaded).exams 
          : <Exam>[];
      
      emit(ExamError(
        message: 'Failed to duplicate exam: ${e.toString()}',
        exams: currentExams,
        failedOperation: 'duplicate',
      ));
    }
  }

  /// Handle starting an exam
  Future<void> _onStartExam(
    StartExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      final startedExam = await _examRepository.startExam(event.examId);
      
      emit(ExamActive(
        exam: startedExam,
        startedAt: DateTime.now(),
      ));
    } catch (e) {
      emit(ExamError(
        message: 'Failed to start exam: ${e.toString()}',
        failedOperation: 'start',
      ));
    }
  }

  /// Handle ending an exam
  Future<void> _onEndExam(
    EndExam event,
    Emitter<ExamState> emit,
  ) async {
    try {
      final endedExam = await _examRepository.endExam(event.examId);
      
      emit(ExamEnded(
        exam: endedExam,
        endedAt: DateTime.now(),
        totalParticipants: 0, // This would come from the repository
      ));
    } catch (e) {
      emit(ExamError(
        message: 'Failed to end exam: ${e.toString()}',
        failedOperation: 'end',
      ));
    }
  }
}
