import 'package:equatable/equatable.dart';
import '../../models/exam_model.dart';

/// Abstract base class for all exam-related states
abstract class ExamState extends Equatable {
  const ExamState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the ExamBloc
class ExamInitial extends ExamState {
  const ExamInitial();

  @override
  String toString() => 'ExamInitial';
}

/// State indicating that exams are being loaded
class ExamLoading extends ExamState {
  const ExamLoading();

  @override
  String toString() => 'ExamLoading';
}

/// State indicating that exams have been loaded successfully
class ExamLoaded extends ExamState {
  /// List of exams
  final List<Exam> exams;
  
  /// Currently selected exam (if any)
  final Exam? selectedExam;
  
  /// Current filter type (e.g., "all", "course", "instructor", "status")
  final String filterType;
  
  /// Current filter value (e.g., course ID, instructor ID, status)
  final String? filterValue;
  
  /// Current search term (if searching)
  final String? searchTerm;
  
  /// Whether the data is being refreshed
  final bool isRefreshing;

  const ExamLoaded({
    required this.exams,
    this.selectedExam,
    this.filterType = 'all',
    this.filterValue,
    this.searchTerm,
    this.isRefreshing = false,
  });

  /// Create a copy of this state with updated values
  ExamLoaded copyWith({
    List<Exam>? exams,
    Exam? selectedExam,
    String? filterType,
    String? filterValue,
    String? searchTerm,
    bool? isRefreshing,
    bool clearSelectedExam = false,
    bool clearFilterValue = false,
    bool clearSearchTerm = false,
  }) {
    return ExamLoaded(
      exams: exams ?? this.exams,
      selectedExam: clearSelectedExam 
          ? null 
          : selectedExam ?? this.selectedExam,
      filterType: filterType ?? this.filterType,
      filterValue: clearFilterValue 
          ? null 
          : filterValue ?? this.filterValue,
      searchTerm: clearSearchTerm 
          ? null 
          : searchTerm ?? this.searchTerm,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  @override
  List<Object?> get props => [
        exams,
        selectedExam,
        filterType,
        filterValue,
        searchTerm,
        isRefreshing,
      ];

  @override
  String toString() => 'ExamLoaded('
      'exams: ${exams.length}, '
      'selectedExam: ${selectedExam?.title}, '
      'filterType: $filterType, '
      'filterValue: $filterValue, '
      'searchTerm: $searchTerm, '
      'isRefreshing: $isRefreshing)';
}

/// State indicating that an exam operation is in progress
class ExamOperationInProgress extends ExamState {
  /// The type of operation being performed
  final String operationType;
  
  /// Optional message describing the operation
  final String? message;

  const ExamOperationInProgress({
    required this.operationType,
    this.message,
  });

  @override
  List<Object?> get props => [operationType, message];

  @override
  String toString() => 'ExamOperationInProgress(operationType: $operationType, message: $message)';
}

/// State indicating that an exam operation was successful
class ExamOperationSuccess extends ExamState {
  /// The type of operation that was performed
  final String operationType;
  
  /// Success message
  final String message;
  
  /// Updated list of exams
  final List<Exam> exams;
  
  /// The exam that was operated on (if applicable)
  final Exam? operatedExam;

  const ExamOperationSuccess({
    required this.operationType,
    required this.message,
    required this.exams,
    this.operatedExam,
  });

  @override
  List<Object?> get props => [operationType, message, exams, operatedExam];

  @override
  String toString() => 'ExamOperationSuccess('
      'operationType: $operationType, '
      'message: $message, '
      'exams: ${exams.length}, '
      'operatedExam: ${operatedExam?.title})';
}

/// State indicating that an error occurred
class ExamError extends ExamState {
  /// Error message describing what went wrong
  final String message;
  
  /// Current list of exams (maintained for UI consistency)
  final List<Exam> exams;
  
  /// The operation that failed
  final String? failedOperation;

  const ExamError({
    required this.message,
    this.exams = const [],
    this.failedOperation,
  });

  @override
  List<Object?> get props => [message, exams, failedOperation];

  @override
  String toString() => 'ExamError('
      'message: $message, '
      'exams: ${exams.length}, '
      'failedOperation: $failedOperation)';
}

/// State indicating that no exams were found
class ExamEmpty extends ExamState {
  /// Message explaining why no exams were found
  final String message;
  
  /// The filter or search criteria that resulted in no results
  final String? criteria;

  const ExamEmpty({
    required this.message,
    this.criteria,
  });

  @override
  List<Object?> get props => [message, criteria];

  @override
  String toString() => 'ExamEmpty(message: $message, criteria: $criteria)';
}

/// State indicating that exam data has been cleared
class ExamCleared extends ExamState {
  const ExamCleared();

  @override
  String toString() => 'ExamCleared';
}

/// State indicating that exam validation is in progress
class ExamValidating extends ExamState {
  final Exam exam;
  final String? validationMessage;

  const ExamValidating({
    required this.exam,
    this.validationMessage,
  });

  @override
  List<Object?> get props => [exam, validationMessage];

  @override
  String toString() => 'ExamValidating('
      'exam: ${exam.title}, '
      'validationMessage: $validationMessage)';
}

/// State indicating exam validation results
class ExamValidated extends ExamState {
  final Exam exam;
  final bool isValid;
  final List<String> validationErrors;
  final List<String> validationWarnings;

  const ExamValidated({
    required this.exam,
    required this.isValid,
    this.validationErrors = const [],
    this.validationWarnings = const [],
  });

  @override
  List<Object?> get props => [
        exam,
        isValid,
        validationErrors,
        validationWarnings,
      ];

  @override
  String toString() => 'ExamValidated('
      'exam: ${exam.title}, '
      'isValid: $isValid, '
      'errors: ${validationErrors.length}, '
      'warnings: ${validationWarnings.length})';
}

/// State indicating that an exam has been published
class ExamPublished extends ExamState {
  final Exam exam;
  final String message;
  final DateTime publishedAt;

  const ExamPublished({
    required this.exam,
    required this.message,
    required this.publishedAt,
  });

  @override
  List<Object?> get props => [exam, message, publishedAt];

  @override
  String toString() => 'ExamPublished('
      'exam: ${exam.title}, '
      'message: $message, '
      'publishedAt: $publishedAt)';
}

/// State indicating that an exam is currently active/running
class ExamActive extends ExamState {
  final Exam exam;
  final DateTime startedAt;
  final Duration? remainingTime;
  final int? activeParticipants;

  const ExamActive({
    required this.exam,
    required this.startedAt,
    this.remainingTime,
    this.activeParticipants,
  });

  @override
  List<Object?> get props => [
        exam,
        startedAt,
        remainingTime,
        activeParticipants,
      ];

  @override
  String toString() => 'ExamActive('
      'exam: ${exam.title}, '
      'startedAt: $startedAt, '
      'remainingTime: $remainingTime, '
      'activeParticipants: $activeParticipants)';
}

/// State indicating that an exam has ended
class ExamEnded extends ExamState {
  final Exam exam;
  final DateTime endedAt;
  final int totalParticipants;
  final String? endReason;

  const ExamEnded({
    required this.exam,
    required this.endedAt,
    required this.totalParticipants,
    this.endReason,
  });

  @override
  List<Object?> get props => [
        exam,
        endedAt,
        totalParticipants,
        endReason,
      ];

  @override
  String toString() => 'ExamEnded('
      'exam: ${exam.title}, '
      'endedAt: $endedAt, '
      'totalParticipants: $totalParticipants, '
      'endReason: $endReason)';
}
