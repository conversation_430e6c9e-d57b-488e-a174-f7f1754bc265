import 'package:flutter_bloc/flutter_bloc.dart';
import '../../repositories/fee_repository.dart';
import '../../models/fee_structure_model.dart';
import 'fee_event.dart';
import 'fee_state.dart';

/// BLoC for managing fee structure operations
/// 
/// This BLoC handles all fee structure related operations including:
/// - Loading fee structures from Firebase
/// - Creating, updating, and deleting fee structures
/// - Filtering and searching fee structures
/// - Managing fee structure state across the application
/// 
/// Uses the Repository pattern to interact with Firebase through FeeRepository.
class FeeBloc extends Bloc<FeeEvent, FeeState> {
  /// Repository for fee structure operations
  final FeeRepository _feeRepository;

  /// Constructor that initializes the BLoC with FeeInitial state
  FeeBloc({required FeeRepository feeRepository})
      : _feeRepository = feeRepository,
        super(const FeeInitial()) {
    
    // Register event handlers
    on<LoadFeeStructures>(_onLoadFeeStructures);
    on<LoadFeeStructuresByAcademicYear>(_onLoadFeeStructuresByAcademicYear);
    on<LoadFeeStructuresByGradeLevel>(_onLoadFeeStructuresByGradeLevel);
    on<LoadActiveFeeStructures>(_onLoadActiveFeeStructures);
    on<CreateFeeStructure>(_onCreateFeeStructure);
    on<UpdateFeeStructure>(_onUpdateFeeStructure);
    on<DeleteFeeStructure>(_onDeleteFeeStructure);
    on<SearchFeeStructures>(_onSearchFeeStructures);
    on<ToggleFeeStructureStatus>(_onToggleFeeStructureStatus);
    on<GetFeeStructureForStudent>(_onGetFeeStructureForStudent);
    on<ClearFeeStructures>(_onClearFeeStructures);
    on<RefreshFeeStructures>(_onRefreshFeeStructures);
    on<SelectFeeStructure>(_onSelectFeeStructure);
  }

  /// Handle loading all fee structures
  Future<void> _onLoadFeeStructures(
    LoadFeeStructures event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeLoading());
      
      final feeStructures = await _feeRepository.getAllFeeStructures();
      
      if (feeStructures.isEmpty) {
        emit(const FeeEmpty(message: 'No fee structures found'));
      } else {
        emit(FeeLoaded(
          feeStructures: feeStructures,
          filterType: 'all',
        ));
      }
    } catch (e) {
      emit(FeeError(
        message: 'Failed to load fee structures: ${e.toString()}',
        failedOperation: 'load_all',
      ));
    }
  }

  /// Handle loading fee structures by academic year
  Future<void> _onLoadFeeStructuresByAcademicYear(
    LoadFeeStructuresByAcademicYear event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeLoading());
      
      final feeStructures = await _feeRepository.getFeeStructuresByAcademicYear(event.academicYear);
      
      if (feeStructures.isEmpty) {
        emit(FeeEmpty(
          message: 'No fee structures found for academic year ${event.academicYear}',
          criteria: event.academicYear,
        ));
      } else {
        emit(FeeLoaded(
          feeStructures: feeStructures,
          filterType: 'academic_year',
          filterValue: event.academicYear,
        ));
      }
    } catch (e) {
      emit(FeeError(
        message: 'Failed to load fee structures by academic year: ${e.toString()}',
        failedOperation: 'load_by_academic_year',
      ));
    }
  }

  /// Handle loading fee structures by grade level
  Future<void> _onLoadFeeStructuresByGradeLevel(
    LoadFeeStructuresByGradeLevel event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeLoading());
      
      final feeStructures = await _feeRepository.getFeeStructuresByGradeLevel(event.gradeLevel);
      
      if (feeStructures.isEmpty) {
        emit(FeeEmpty(
          message: 'No fee structures found for grade level ${event.gradeLevel}',
          criteria: event.gradeLevel,
        ));
      } else {
        emit(FeeLoaded(
          feeStructures: feeStructures,
          filterType: 'grade_level',
          filterValue: event.gradeLevel,
        ));
      }
    } catch (e) {
      emit(FeeError(
        message: 'Failed to load fee structures by grade level: ${e.toString()}',
        failedOperation: 'load_by_grade_level',
      ));
    }
  }

  /// Handle loading active fee structures
  Future<void> _onLoadActiveFeeStructures(
    LoadActiveFeeStructures event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeLoading());
      
      final feeStructures = await _feeRepository.getActiveFeeStructures();
      
      if (feeStructures.isEmpty) {
        emit(const FeeEmpty(message: 'No active fee structures found'));
      } else {
        emit(FeeLoaded(
          feeStructures: feeStructures,
          filterType: 'active',
        ));
      }
    } catch (e) {
      emit(FeeError(
        message: 'Failed to load active fee structures: ${e.toString()}',
        failedOperation: 'load_active',
      ));
    }
  }

  /// Handle creating a new fee structure
  Future<void> _onCreateFeeStructure(
    CreateFeeStructure event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeOperationInProgress(
        operationType: 'create',
        message: 'Creating fee structure...',
      ));
      
      final createdFeeStructure = await _feeRepository.createFeeStructure(event.feeStructure);
      
      // Reload all fee structures to get updated list
      final allFeeStructures = await _feeRepository.getAllFeeStructures();
      
      emit(FeeOperationSuccess(
        operationType: 'create',
        message: 'Fee structure created successfully',
        feeStructures: allFeeStructures,
        operatedFeeStructure: createdFeeStructure,
      ));
      
      // Transition to loaded state
      emit(FeeLoaded(
        feeStructures: allFeeStructures,
        selectedFeeStructure: createdFeeStructure,
      ));
      
    } catch (e) {
      final currentFeeStructures = state is FeeLoaded 
          ? (state as FeeLoaded).feeStructures 
          : <FeeStructure>[];
      
      emit(FeeError(
        message: 'Failed to create fee structure: ${e.toString()}',
        feeStructures: currentFeeStructures,
        failedOperation: 'create',
      ));
    }
  }

  /// Handle updating an existing fee structure
  Future<void> _onUpdateFeeStructure(
    UpdateFeeStructure event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeOperationInProgress(
        operationType: 'update',
        message: 'Updating fee structure...',
      ));
      
      final updatedFeeStructure = await _feeRepository.updateFeeStructure(event.feeStructure);
      
      // Reload all fee structures to get updated list
      final allFeeStructures = await _feeRepository.getAllFeeStructures();
      
      emit(FeeOperationSuccess(
        operationType: 'update',
        message: 'Fee structure updated successfully',
        feeStructures: allFeeStructures,
        operatedFeeStructure: updatedFeeStructure,
      ));
      
      // Transition to loaded state
      emit(FeeLoaded(
        feeStructures: allFeeStructures,
        selectedFeeStructure: updatedFeeStructure,
      ));
      
    } catch (e) {
      final currentFeeStructures = state is FeeLoaded 
          ? (state as FeeLoaded).feeStructures 
          : <FeeStructure>[];
      
      emit(FeeError(
        message: 'Failed to update fee structure: ${e.toString()}',
        feeStructures: currentFeeStructures,
        failedOperation: 'update',
      ));
    }
  }

  /// Handle deleting a fee structure
  Future<void> _onDeleteFeeStructure(
    DeleteFeeStructure event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeOperationInProgress(
        operationType: 'delete',
        message: 'Deleting fee structure...',
      ));
      
      await _feeRepository.deleteFeeStructure(event.feeStructureId);
      
      // Reload all fee structures to get updated list
      final allFeeStructures = await _feeRepository.getAllFeeStructures();
      
      emit(FeeOperationSuccess(
        operationType: 'delete',
        message: 'Fee structure deleted successfully',
        feeStructures: allFeeStructures,
      ));
      
      // Transition to loaded state
      if (allFeeStructures.isEmpty) {
        emit(const FeeEmpty(message: 'No fee structures found'));
      } else {
        emit(FeeLoaded(feeStructures: allFeeStructures));
      }
      
    } catch (e) {
      final currentFeeStructures = state is FeeLoaded 
          ? (state as FeeLoaded).feeStructures 
          : <FeeStructure>[];
      
      emit(FeeError(
        message: 'Failed to delete fee structure: ${e.toString()}',
        feeStructures: currentFeeStructures,
        failedOperation: 'delete',
      ));
    }
  }

  /// Handle searching fee structures
  Future<void> _onSearchFeeStructures(
    SearchFeeStructures event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeLoading());
      
      final feeStructures = await _feeRepository.searchFeeStructures(event.searchTerm);
      
      if (feeStructures.isEmpty) {
        emit(FeeEmpty(
          message: 'No fee structures found matching "${event.searchTerm}"',
          criteria: event.searchTerm,
        ));
      } else {
        emit(FeeLoaded(
          feeStructures: feeStructures,
          filterType: 'search',
          searchTerm: event.searchTerm,
        ));
      }
    } catch (e) {
      emit(FeeError(
        message: 'Failed to search fee structures: ${e.toString()}',
        failedOperation: 'search',
      ));
    }
  }

  /// Handle toggling fee structure status
  Future<void> _onToggleFeeStructureStatus(
    ToggleFeeStructureStatus event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeOperationInProgress(
        operationType: 'toggle_status',
        message: 'Updating fee structure status...',
      ));
      
      final updatedFeeStructure = await _feeRepository.toggleFeeStructureStatus(event.feeStructureId);
      
      // Reload all fee structures to get updated list
      final allFeeStructures = await _feeRepository.getAllFeeStructures();
      
      emit(FeeOperationSuccess(
        operationType: 'toggle_status',
        message: 'Fee structure status updated successfully',
        feeStructures: allFeeStructures,
        operatedFeeStructure: updatedFeeStructure,
      ));
      
      // Transition to loaded state
      emit(FeeLoaded(
        feeStructures: allFeeStructures,
        selectedFeeStructure: updatedFeeStructure,
      ));
      
    } catch (e) {
      final currentFeeStructures = state is FeeLoaded 
          ? (state as FeeLoaded).feeStructures 
          : <FeeStructure>[];
      
      emit(FeeError(
        message: 'Failed to toggle fee structure status: ${e.toString()}',
        feeStructures: currentFeeStructures,
        failedOperation: 'toggle_status',
      ));
    }
  }

  /// Handle getting fee structure for student
  Future<void> _onGetFeeStructureForStudent(
    GetFeeStructureForStudent event,
    Emitter<FeeState> emit,
  ) async {
    try {
      emit(const FeeLoading());
      
      final feeStructure = await _feeRepository.getFeeStructureForStudent(
        event.gradeLevel,
        event.academicYear,
      );
      
      if (feeStructure == null) {
        emit(FeeEmpty(
          message: 'No fee structure found for grade ${event.gradeLevel} in ${event.academicYear}',
          criteria: '${event.gradeLevel} - ${event.academicYear}',
        ));
      } else {
        emit(FeeLoaded(
          feeStructures: [feeStructure],
          selectedFeeStructure: feeStructure,
          filterType: 'student',
          filterValue: '${event.gradeLevel} - ${event.academicYear}',
        ));
      }
    } catch (e) {
      emit(FeeError(
        message: 'Failed to get fee structure for student: ${e.toString()}',
        failedOperation: 'get_for_student',
      ));
    }
  }

  /// Handle clearing fee structures
  Future<void> _onClearFeeStructures(
    ClearFeeStructures event,
    Emitter<FeeState> emit,
  ) async {
    emit(const FeeCleared());
  }

  /// Handle refreshing fee structures
  Future<void> _onRefreshFeeStructures(
    RefreshFeeStructures event,
    Emitter<FeeState> emit,
  ) async {
    if (state is FeeLoaded) {
      final currentState = state as FeeLoaded;
      emit(currentState.copyWith(isRefreshing: true));
      
      try {
        final feeStructures = await _feeRepository.getAllFeeStructures();
        
        emit(currentState.copyWith(
          feeStructures: feeStructures,
          isRefreshing: false,
        ));
      } catch (e) {
        emit(FeeError(
          message: 'Failed to refresh fee structures: ${e.toString()}',
          feeStructures: currentState.feeStructures,
          failedOperation: 'refresh',
        ));
      }
    } else {
      // If not in loaded state, perform regular load
      add(const LoadFeeStructures());
    }
  }

  /// Handle selecting a fee structure
  Future<void> _onSelectFeeStructure(
    SelectFeeStructure event,
    Emitter<FeeState> emit,
  ) async {
    if (state is FeeLoaded) {
      final currentState = state as FeeLoaded;
      emit(currentState.copyWith(
        selectedFeeStructure: event.feeStructure,
      ));
    }
  }
}
