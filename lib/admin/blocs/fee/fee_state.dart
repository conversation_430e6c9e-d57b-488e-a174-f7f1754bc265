import 'package:equatable/equatable.dart';
import '../../models/fee_structure_model.dart';

/// Abstract base class for all fee-related states
abstract class FeeState extends Equatable {
  const FeeState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the FeeBloc
class FeeInitial extends FeeState {
  const FeeInitial();

  @override
  String toString() => 'FeeInitial';
}

/// State indicating that fee structures are being loaded
class FeeLoading extends FeeState {
  const FeeLoading();

  @override
  String toString() => 'FeeLoading';
}

/// State indicating that fee structures have been loaded successfully
class FeeLoaded extends FeeState {
  /// List of fee structures
  final List<FeeStructure> feeStructures;
  
  /// Currently selected fee structure (if any)
  final FeeStructure? selectedFeeStructure;
  
  /// Current filter type (e.g., "all", "active", "academic_year", "grade_level")
  final String filterType;
  
  /// Current filter value (e.g., academic year or grade level)
  final String? filterValue;
  
  /// Current search term (if searching)
  final String? searchTerm;
  
  /// Whether the data is being refreshed
  final bool isRefreshing;

  const FeeLoaded({
    required this.feeStructures,
    this.selectedFeeStructure,
    this.filterType = 'all',
    this.filterValue,
    this.searchTerm,
    this.isRefreshing = false,
  });

  /// Create a copy of this state with updated values
  FeeLoaded copyWith({
    List<FeeStructure>? feeStructures,
    FeeStructure? selectedFeeStructure,
    String? filterType,
    String? filterValue,
    String? searchTerm,
    bool? isRefreshing,
    bool clearSelectedFeeStructure = false,
    bool clearFilterValue = false,
    bool clearSearchTerm = false,
  }) {
    return FeeLoaded(
      feeStructures: feeStructures ?? this.feeStructures,
      selectedFeeStructure: clearSelectedFeeStructure 
          ? null 
          : selectedFeeStructure ?? this.selectedFeeStructure,
      filterType: filterType ?? this.filterType,
      filterValue: clearFilterValue 
          ? null 
          : filterValue ?? this.filterValue,
      searchTerm: clearSearchTerm 
          ? null 
          : searchTerm ?? this.searchTerm,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  @override
  List<Object?> get props => [
        feeStructures,
        selectedFeeStructure,
        filterType,
        filterValue,
        searchTerm,
        isRefreshing,
      ];

  @override
  String toString() => 'FeeLoaded('
      'feeStructures: ${feeStructures.length}, '
      'selectedFeeStructure: ${selectedFeeStructure?.name}, '
      'filterType: $filterType, '
      'filterValue: $filterValue, '
      'searchTerm: $searchTerm, '
      'isRefreshing: $isRefreshing)';
}

/// State indicating that a fee structure operation is in progress
class FeeOperationInProgress extends FeeState {
  /// The type of operation being performed
  final String operationType;
  
  /// Optional message describing the operation
  final String? message;

  const FeeOperationInProgress({
    required this.operationType,
    this.message,
  });

  @override
  List<Object?> get props => [operationType, message];

  @override
  String toString() => 'FeeOperationInProgress(operationType: $operationType, message: $message)';
}

/// State indicating that a fee structure operation was successful
class FeeOperationSuccess extends FeeState {
  /// The type of operation that was performed
  final String operationType;
  
  /// Success message
  final String message;
  
  /// Updated list of fee structures
  final List<FeeStructure> feeStructures;
  
  /// The fee structure that was operated on (if applicable)
  final FeeStructure? operatedFeeStructure;

  const FeeOperationSuccess({
    required this.operationType,
    required this.message,
    required this.feeStructures,
    this.operatedFeeStructure,
  });

  @override
  List<Object?> get props => [operationType, message, feeStructures, operatedFeeStructure];

  @override
  String toString() => 'FeeOperationSuccess('
      'operationType: $operationType, '
      'message: $message, '
      'feeStructures: ${feeStructures.length}, '
      'operatedFeeStructure: ${operatedFeeStructure?.name})';
}

/// State indicating that an error occurred
class FeeError extends FeeState {
  /// Error message describing what went wrong
  final String message;
  
  /// Current list of fee structures (maintained for UI consistency)
  final List<FeeStructure> feeStructures;
  
  /// The operation that failed
  final String? failedOperation;

  const FeeError({
    required this.message,
    this.feeStructures = const [],
    this.failedOperation,
  });

  @override
  List<Object?> get props => [message, feeStructures, failedOperation];

  @override
  String toString() => 'FeeError('
      'message: $message, '
      'feeStructures: ${feeStructures.length}, '
      'failedOperation: $failedOperation)';
}

/// State indicating that no fee structures were found
class FeeEmpty extends FeeState {
  /// Message explaining why no fee structures were found
  final String message;
  
  /// The filter or search criteria that resulted in no results
  final String? criteria;

  const FeeEmpty({
    required this.message,
    this.criteria,
  });

  @override
  List<Object?> get props => [message, criteria];

  @override
  String toString() => 'FeeEmpty(message: $message, criteria: $criteria)';
}

/// State indicating that fee structure data has been cleared
class FeeCleared extends FeeState {
  const FeeCleared();

  @override
  String toString() => 'FeeCleared';
}
