import 'package:equatable/equatable.dart';
import '../../models/fee_structure_model.dart';

/// Abstract base class for all fee-related events
/// 
/// This class defines the contract for all events that can be dispatched
/// to the FeeBloc for fee structure management operations.
abstract class FeeEvent extends Equatable {
  const FeeEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all fee structures
/// 
/// Dispatched when the app needs to fetch and display all fee structures
/// from Firebase. This is typically called when the fee management screen
/// is first loaded or when a refresh is needed.
class LoadFeeStructures extends FeeEvent {
  const LoadFeeStructures();

  @override
  String toString() => 'LoadFeeStructures';
}

/// Event to load fee structures for a specific academic year
/// 
/// Dispatched when filtering fee structures by academic year.
/// Takes an [academicYear] parameter to filter the results.
class LoadFeeStructuresByAcademicYear extends FeeEvent {
  /// The academic year to filter by (e.g., "2023-2024")
  final String academicYear;

  const LoadFeeStructuresByAcademicYear(this.academicYear);

  @override
  List<Object?> get props => [academicYear];

  @override
  String toString() => 'LoadFeeStructuresByAcademicYear(academicYear: $academicYear)';
}

/// Event to load fee structures for a specific grade level
/// 
/// Dispatched when filtering fee structures by grade level.
/// Takes a [gradeLevel] parameter to filter the results.
class LoadFeeStructuresByGradeLevel extends FeeEvent {
  /// The grade level to filter by (e.g., "Grade 1", "Grade 10")
  final String gradeLevel;

  const LoadFeeStructuresByGradeLevel(this.gradeLevel);

  @override
  List<Object?> get props => [gradeLevel];

  @override
  String toString() => 'LoadFeeStructuresByGradeLevel(gradeLevel: $gradeLevel)';
}

/// Event to load only active fee structures
/// 
/// Dispatched when only active fee structures need to be displayed.
/// This filters out inactive or archived fee structures.
class LoadActiveFeeStructures extends FeeEvent {
  const LoadActiveFeeStructures();

  @override
  String toString() => 'LoadActiveFeeStructures';
}

/// Event to create a new fee structure
/// 
/// Dispatched when a new fee structure needs to be created.
/// Takes a [feeStructure] parameter containing the fee structure data.
class CreateFeeStructure extends FeeEvent {
  /// The fee structure to be created
  final FeeStructure feeStructure;

  const CreateFeeStructure(this.feeStructure);

  @override
  List<Object?> get props => [feeStructure];

  @override
  String toString() => 'CreateFeeStructure(feeStructure: ${feeStructure.name})';
}

/// Event to update an existing fee structure
/// 
/// Dispatched when an existing fee structure needs to be updated.
/// Takes a [feeStructure] parameter containing the updated fee structure data.
class UpdateFeeStructure extends FeeEvent {
  /// The fee structure to be updated
  final FeeStructure feeStructure;

  const UpdateFeeStructure(this.feeStructure);

  @override
  List<Object?> get props => [feeStructure];

  @override
  String toString() => 'UpdateFeeStructure(feeStructure: ${feeStructure.name})';
}

/// Event to delete a fee structure
/// 
/// Dispatched when a fee structure needs to be deleted.
/// Takes a [feeStructureId] parameter to identify which fee structure to delete.
class DeleteFeeStructure extends FeeEvent {
  /// The ID of the fee structure to be deleted
  final String feeStructureId;

  const DeleteFeeStructure(this.feeStructureId);

  @override
  List<Object?> get props => [feeStructureId];

  @override
  String toString() => 'DeleteFeeStructure(feeStructureId: $feeStructureId)';
}

/// Event to search fee structures
/// 
/// Dispatched when searching for fee structures by name or other criteria.
/// Takes a [searchTerm] parameter for the search query.
class SearchFeeStructures extends FeeEvent {
  /// The search term to filter fee structures
  final String searchTerm;

  const SearchFeeStructures(this.searchTerm);

  @override
  List<Object?> get props => [searchTerm];

  @override
  String toString() => 'SearchFeeStructures(searchTerm: $searchTerm)';
}

/// Event to toggle the active status of a fee structure
/// 
/// Dispatched when the active status of a fee structure needs to be toggled.
/// Takes a [feeStructureId] parameter to identify which fee structure to toggle.
class ToggleFeeStructureStatus extends FeeEvent {
  /// The ID of the fee structure to toggle status
  final String feeStructureId;

  const ToggleFeeStructureStatus(this.feeStructureId);

  @override
  List<Object?> get props => [feeStructureId];

  @override
  String toString() => 'ToggleFeeStructureStatus(feeStructureId: $feeStructureId)';
}

/// Event to get fee structure for a specific student
/// 
/// Dispatched when retrieving the applicable fee structure for a student
/// based on their grade level and academic year.
class GetFeeStructureForStudent extends FeeEvent {
  /// The student's grade level
  final String gradeLevel;
  
  /// The academic year
  final String academicYear;

  const GetFeeStructureForStudent({
    required this.gradeLevel,
    required this.academicYear,
  });

  @override
  List<Object?> get props => [gradeLevel, academicYear];

  @override
  String toString() => 'GetFeeStructureForStudent(gradeLevel: $gradeLevel, academicYear: $academicYear)';
}

/// Event to clear fee structure data
/// 
/// Dispatched when fee structure data needs to be cleared,
/// typically when navigating away from the fee management screen.
class ClearFeeStructures extends FeeEvent {
  const ClearFeeStructures();

  @override
  String toString() => 'ClearFeeStructures';
}

/// Event to refresh fee structure data
/// 
/// Dispatched when fee structure data needs to be refreshed,
/// typically triggered by pull-to-refresh or manual refresh actions.
class RefreshFeeStructures extends FeeEvent {
  const RefreshFeeStructures();

  @override
  String toString() => 'RefreshFeeStructures';
}

/// Event to select a fee structure
/// 
/// Dispatched when a specific fee structure is selected for viewing or editing.
/// Takes a [feeStructure] parameter for the selected fee structure.
class SelectFeeStructure extends FeeEvent {
  /// The selected fee structure
  final FeeStructure? feeStructure;

  const SelectFeeStructure(this.feeStructure);

  @override
  List<Object?> get props => [feeStructure];

  @override
  String toString() => 'SelectFeeStructure(feeStructure: ${feeStructure?.name})';
}

/// Event to validate fee structure data
/// 
/// Dispatched when fee structure data needs to be validated
/// before creation or update operations.
class ValidateFeeStructure extends FeeEvent {
  /// The fee structure to validate
  final FeeStructure feeStructure;

  const ValidateFeeStructure(this.feeStructure);

  @override
  List<Object?> get props => [feeStructure];

  @override
  String toString() => 'ValidateFeeStructure(feeStructure: ${feeStructure.name})';
}
