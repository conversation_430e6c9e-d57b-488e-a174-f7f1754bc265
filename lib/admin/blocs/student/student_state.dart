import 'package:equatable/equatable.dart';
import 'dart:io';
import '../../models/student_model.dart';
import '../../models/id_card_model.dart';

/// Student states for BLoC state management
/// 
/// This file defines all possible states in the student management system,
/// providing a reactive and predictable state management solution.
abstract class StudentState extends Equatable {
  const StudentState();

  @override
  List<Object?> get props => [];
}

/// Initial state when StudentBloc is first created
class StudentInitial extends StudentState {
  const StudentInitial();

  @override
  String toString() => 'StudentInitial';
}

/// State when students are being loaded
class StudentLoading extends StudentState {
  const StudentLoading();

  @override
  String toString() => 'StudentLoading';
}

/// State when students have been successfully loaded
class StudentLoaded extends StudentState {
  /// List of all students
  final List<Student> students;
  
  /// Filtered students based on search/filter criteria
  final List<Student> filteredStudents;
  
  /// Current search query
  final String? searchQuery;
  
  /// Current filter criteria
  final String? filterCriteria;
  
  /// Whether there are active filters
  final bool hasActiveFilters;
  
  /// Total number of students
  final int totalStudents;
  
  /// Number of active students
  final int activeStudents;
  
  /// Number of inactive students
  final int inactiveStudents;

  StudentLoaded({
    required this.students,
    List<Student>? filteredStudents,
    this.searchQuery,
    this.filterCriteria,
    this.hasActiveFilters = false,
  }) : filteredStudents = filteredStudents ?? students,
       totalStudents = students.length,
       activeStudents = students.where((s) => s.status == StudentStatus.active).length,
       inactiveStudents = students.where((s) => s.status != StudentStatus.active).length;

  /// Get filtered students based on current criteria
  List<Student> get displayStudents {
    var filtered = List<Student>.from(students);

    // Apply search filter
    if (searchQuery?.isNotEmpty == true) {
      final query = searchQuery!.toLowerCase();
      filtered = filtered.where((student) {
        return student.fullName.toLowerCase().contains(query) ||
               student.email.toLowerCase().contains(query) ||
               student.studentId.toLowerCase().contains(query) ||
               (student.phoneNumber?.toLowerCase().contains(query) ?? false) ||
               (student.major?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply status filter
    if (filterCriteria?.isNotEmpty == true) {
      if (filterCriteria == 'active') {
        filtered = filtered.where((student) => student.status == StudentStatus.active).toList();
      } else if (filterCriteria == 'inactive') {
        filtered = filtered.where((student) => student.status != StudentStatus.active).toList();
      } else if (filterCriteria == 'graduated') {
        filtered = filtered.where((student) => student.status == StudentStatus.graduated).toList();
      }
    }

    return filtered;
  }

  /// Check if there are any active filters or search
  bool get hasFiltersOrSearch => 
      (searchQuery?.isNotEmpty ?? false) || 
      (filterCriteria?.isNotEmpty ?? false);

  /// Create a copy with updated fields
  StudentLoaded copyWith({
    List<Student>? students,
    List<Student>? filteredStudents,
    String? searchQuery,
    String? filterCriteria,
    bool? hasActiveFilters,
  }) {
    return StudentLoaded(
      students: students ?? this.students,
      filteredStudents: filteredStudents ?? this.filteredStudents,
      searchQuery: searchQuery ?? this.searchQuery,
      filterCriteria: filterCriteria ?? this.filterCriteria,
      hasActiveFilters: hasActiveFilters ?? this.hasActiveFilters,
    );
  }

  @override
  List<Object?> get props => [
    students,
    filteredStudents,
    searchQuery,
    filterCriteria,
    hasActiveFilters,
  ];

  @override
  String toString() => 'StudentLoaded { students: ${students.length}, filtered: ${filteredStudents.length} }';
}

/// State when a student is being created
class StudentCreating extends StudentState {
  /// Student being created
  final Student student;

  const StudentCreating(this.student);

  @override
  List<Object> get props => [student];

  @override
  String toString() => 'StudentCreating { student: ${student.fullName} }';
}

/// State when a student has been successfully created
class StudentCreated extends StudentState {
  /// Created student
  final Student student;
  
  /// Success message
  final String message;

  const StudentCreated({
    required this.student,
    this.message = 'Student created successfully',
  });

  @override
  List<Object> get props => [student, message];

  @override
  String toString() => 'StudentCreated { student: ${student.fullName} }';
}

/// State when a student is being updated
class StudentUpdating extends StudentState {
  /// Student being updated
  final Student student;

  const StudentUpdating(this.student);

  @override
  List<Object> get props => [student];

  @override
  String toString() => 'StudentUpdating { student: ${student.fullName} }';
}

/// State when a student has been successfully updated
class StudentUpdated extends StudentState {
  /// Updated student
  final Student student;
  
  /// Success message
  final String message;

  const StudentUpdated({
    required this.student,
    this.message = 'Student updated successfully',
  });

  @override
  List<Object> get props => [student, message];

  @override
  String toString() => 'StudentUpdated { student: ${student.fullName} }';
}

/// State when a student is being deleted
class StudentDeleting extends StudentState {
  /// Student ID being deleted
  final String studentId;

  const StudentDeleting(this.studentId);

  @override
  List<Object> get props => [studentId];

  @override
  String toString() => 'StudentDeleting { studentId: $studentId }';
}

/// State when a student has been successfully deleted
class StudentDeleted extends StudentState {
  /// Deleted student ID
  final String studentId;
  
  /// Success message
  final String message;

  const StudentDeleted({
    required this.studentId,
    this.message = 'Student deleted successfully',
  });

  @override
  List<Object> get props => [studentId, message];

  @override
  String toString() => 'StudentDeleted { studentId: $studentId }';
}

/// State when a single student is being loaded
class StudentDetailLoading extends StudentState {
  /// Student ID being loaded
  final String studentId;

  const StudentDetailLoading(this.studentId);

  @override
  List<Object> get props => [studentId];

  @override
  String toString() => 'StudentDetailLoading { studentId: $studentId }';
}

/// State when a single student has been loaded
class StudentDetailLoaded extends StudentState {
  /// Loaded student
  final Student student;

  const StudentDetailLoaded(this.student);

  @override
  List<Object> get props => [student];

  @override
  String toString() => 'StudentDetailLoaded { student: ${student.fullName} }';
}

/// State when students are being searched
class StudentSearching extends StudentState {
  /// Search query
  final String query;

  const StudentSearching(this.query);

  @override
  List<Object> get props => [query];

  @override
  String toString() => 'StudentSearching { query: $query }';
}

/// State when student enrollment is being processed
class StudentEnrollmentProcessing extends StudentState {
  /// Student ID being enrolled/unenrolled
  final String studentId;
  
  /// Course ID for enrollment
  final String courseId;
  
  /// Whether this is enrollment (true) or unenrollment (false)
  final bool isEnrollment;

  const StudentEnrollmentProcessing({
    required this.studentId,
    required this.courseId,
    required this.isEnrollment,
  });

  @override
  List<Object> get props => [studentId, courseId, isEnrollment];

  @override
  String toString() => 'StudentEnrollmentProcessing { studentId: $studentId, courseId: $courseId, isEnrollment: $isEnrollment }';
}

/// State when student enrollment has been completed
class StudentEnrollmentCompleted extends StudentState {
  /// Student ID that was enrolled/unenrolled
  final String studentId;
  
  /// Course ID for enrollment
  final String courseId;
  
  /// Whether this was enrollment (true) or unenrollment (false)
  final bool wasEnrollment;
  
  /// Success message
  final String message;

  const StudentEnrollmentCompleted({
    required this.studentId,
    required this.courseId,
    required this.wasEnrollment,
    required this.message,
  });

  @override
  List<Object> get props => [studentId, courseId, wasEnrollment, message];

  @override
  String toString() => 'StudentEnrollmentCompleted { studentId: $studentId, courseId: $courseId, wasEnrollment: $wasEnrollment }';
}

/// State when bulk operations are being processed
class StudentBulkOperationProcessing extends StudentState {
  /// Number of students being processed
  final int studentCount;

  /// Operation type
  final String operationType;

  const StudentBulkOperationProcessing({
    required this.studentCount,
    required this.operationType,
  });

  @override
  List<Object> get props => [studentCount, operationType];

  @override
  String toString() => 'StudentBulkOperationProcessing { studentCount: $studentCount, operationType: $operationType }';
}

/// State when ID card generation is in progress
class IDCardGenerating extends StudentState {
  /// Student ID for which card is being generated
  final String studentId;

  /// Template being used
  final IDCardTemplate template;

  /// Export format
  final ExportFormat exportFormat;

  const IDCardGenerating({
    required this.studentId,
    required this.template,
    required this.exportFormat,
  });

  @override
  List<Object> get props => [studentId, template, exportFormat];

  @override
  String toString() => 'IDCardGenerating { studentId: $studentId, template: ${template.name}, format: ${exportFormat.name} }';
}

/// State when ID card has been successfully generated
class IDCardGenerated extends StudentState {
  /// Generated ID card file
  final File idCardFile;

  /// Student ID for which card was generated
  final String studentId;

  /// Template that was used
  final IDCardTemplate template;

  /// Export format that was used
  final ExportFormat exportFormat;

  /// Success message
  final String message;

  const IDCardGenerated({
    required this.idCardFile,
    required this.studentId,
    required this.template,
    required this.exportFormat,
    required this.message,
  });

  @override
  List<Object> get props => [idCardFile, studentId, template, exportFormat, message];

  @override
  String toString() => 'IDCardGenerated { studentId: $studentId, filePath: ${idCardFile.path} }';
}

/// State when batch ID card generation is in progress
class BatchIDCardGenerating extends StudentState {
  /// Number of students being processed
  final int totalStudents;

  /// Number of cards generated so far
  final int generatedCount;

  /// Template being used
  final IDCardTemplate template;

  /// Export format
  final ExportFormat exportFormat;

  const BatchIDCardGenerating({
    required this.totalStudents,
    required this.generatedCount,
    required this.template,
    required this.exportFormat,
  });

  @override
  List<Object> get props => [totalStudents, generatedCount, template, exportFormat];

  @override
  String toString() => 'BatchIDCardGenerating { progress: $generatedCount/$totalStudents }';
}

/// State when batch ID card generation is completed
class BatchIDCardGenerated extends StudentState {
  /// List of generated ID card files
  final List<File> idCardFiles;

  /// Number of successfully generated cards
  final int successCount;

  /// Number of failed generations
  final int failureCount;

  /// Template that was used
  final IDCardTemplate template;

  /// Export format that was used
  final ExportFormat exportFormat;

  /// Success message
  final String message;

  const BatchIDCardGenerated({
    required this.idCardFiles,
    required this.successCount,
    required this.failureCount,
    required this.template,
    required this.exportFormat,
    required this.message,
  });

  @override
  List<Object> get props => [idCardFiles, successCount, failureCount, template, exportFormat, message];

  @override
  String toString() => 'BatchIDCardGenerated { success: $successCount, failed: $failureCount }';
}

/// State when ID card preview is ready
class IDCardPreviewReady extends StudentState {
  /// Preview ID card data
  final IDCard previewCard;

  /// Student for which preview was generated
  final Student student;

  const IDCardPreviewReady({
    required this.previewCard,
    required this.student,
  });

  @override
  List<Object> get props => [previewCard, student];

  @override
  String toString() => 'IDCardPreviewReady { studentId: ${student.id} }';
}

/// State when bulk operations have been completed
class StudentBulkOperationCompleted extends StudentState {
  /// Number of students processed
  final int studentCount;
  
  /// Operation type
  final String operationType;
  
  /// Success message
  final String message;

  const StudentBulkOperationCompleted({
    required this.studentCount,
    required this.operationType,
    required this.message,
  });

  @override
  List<Object> get props => [studentCount, operationType, message];

  @override
  String toString() => 'StudentBulkOperationCompleted { studentCount: $studentCount, operationType: $operationType }';
}

/// State when an error occurs in student operations
class StudentError extends StudentState {
  /// Error message
  final String message;
  
  /// Previous state before error (for recovery)
  final StudentState? previousState;
  
  /// Error code for specific handling
  final String? errorCode;
  
  /// Additional error details
  final Map<String, dynamic>? errorDetails;

  const StudentError({
    required this.message,
    this.previousState,
    this.errorCode,
    this.errorDetails,
  });

  @override
  List<Object?> get props => [message, previousState, errorCode, errorDetails];

  @override
  String toString() => 'StudentError { message: $message, errorCode: $errorCode }';
}
