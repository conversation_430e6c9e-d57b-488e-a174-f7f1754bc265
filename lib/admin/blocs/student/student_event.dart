import 'package:equatable/equatable.dart';
import '../../models/student_model.dart';
import '../../models/id_card_model.dart';

/// Student events for BLoC state management
/// 
/// This file defines all possible events that can be triggered
/// in the student management system, following the BLoC pattern
/// for clean separation of concerns and reactive programming.
abstract class StudentEvent extends Equatable {
  const StudentEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all students
class LoadStudentsEvent extends StudentEvent {
  const LoadStudentsEvent();

  @override
  String toString() => 'LoadStudentsEvent';
}

/// Event to refresh students data
class RefreshStudentsEvent extends StudentEvent {
  const RefreshStudentsEvent();

  @override
  String toString() => 'RefreshStudentsEvent';
}

/// Event to create a new student
class CreateStudentEvent extends StudentEvent {
  /// Student object to be created
  final Student student;

  const CreateStudentEvent(this.student);

  @override
  List<Object> get props => [student];

  @override
  String toString() => 'CreateStudentEvent { student: ${student.fullName} }';
}

/// Event to update an existing student
class UpdateStudentEvent extends StudentEvent {
  /// Updated student object
  final Student student;

  const UpdateStudentEvent(this.student);

  @override
  List<Object> get props => [student];

  @override
  String toString() => 'UpdateStudentEvent { student: ${student.fullName} }';
}

/// Event to delete a student
class DeleteStudentEvent extends StudentEvent {
  /// Student ID to be deleted
  final String studentId;

  const DeleteStudentEvent(this.studentId);

  @override
  List<Object> get props => [studentId];

  @override
  String toString() => 'DeleteStudentEvent { studentId: $studentId }';
}

/// Event to search students by query
class SearchStudentsEvent extends StudentEvent {
  /// Search query string
  final String query;

  const SearchStudentsEvent(this.query);

  @override
  List<Object> get props => [query];

  @override
  String toString() => 'SearchStudentsEvent { query: $query }';
}

/// Event to clear search results
class ClearSearchEvent extends StudentEvent {
  const ClearSearchEvent();

  @override
  String toString() => 'ClearSearchEvent';
}

/// Event to filter students by status
class FilterStudentsByStatusEvent extends StudentEvent {
  /// Student status to filter by
  final StudentStatus status;

  const FilterStudentsByStatusEvent(this.status);

  @override
  List<Object> get props => [status];

  @override
  String toString() => 'FilterStudentsByStatusEvent { status: ${status.displayName} }';
}

/// Event to filter students by course
class FilterStudentsByCourseEvent extends StudentEvent {
  /// Course ID to filter by
  final String courseId;

  const FilterStudentsByCourseEvent(this.courseId);

  @override
  List<Object> get props => [courseId];

  @override
  String toString() => 'FilterStudentsByCourseEvent { courseId: $courseId }';
}

/// Event to get students by grade level
class LoadStudentsByGradeLevelEvent extends StudentEvent {
  /// Grade level to filter by
  final String gradeLevel;

  const LoadStudentsByGradeLevelEvent(this.gradeLevel);

  @override
  List<Object> get props => [gradeLevel];

  @override
  String toString() => 'LoadStudentsByGradeLevelEvent { gradeLevel: $gradeLevel }';
}

/// Event to get active students only
class LoadActiveStudentsEvent extends StudentEvent {
  const LoadActiveStudentsEvent();

  @override
  String toString() => 'LoadActiveStudentsEvent';
}

/// Event to get inactive students only
class LoadInactiveStudentsEvent extends StudentEvent {
  const LoadInactiveStudentsEvent();

  @override
  String toString() => 'LoadInactiveStudentsEvent';
}

/// Event to toggle student status (active/inactive)
class ToggleStudentStatusEvent extends StudentEvent {
  /// Student ID to toggle status
  final String studentId;

  const ToggleStudentStatusEvent(this.studentId);

  @override
  List<Object> get props => [studentId];

  @override
  String toString() => 'ToggleStudentStatusEvent { studentId: $studentId }';
}

/// Event to enroll student in a course
class EnrollStudentInCourseEvent extends StudentEvent {
  /// Student ID to enroll
  final String studentId;
  
  /// Course ID to enroll in
  final String courseId;

  const EnrollStudentInCourseEvent({
    required this.studentId,
    required this.courseId,
  });

  @override
  List<Object> get props => [studentId, courseId];

  @override
  String toString() => 'EnrollStudentInCourseEvent { studentId: $studentId, courseId: $courseId }';
}

/// Event to unenroll student from a course
class UnenrollStudentFromCourseEvent extends StudentEvent {
  /// Student ID to unenroll
  final String studentId;
  
  /// Course ID to unenroll from
  final String courseId;

  const UnenrollStudentFromCourseEvent({
    required this.studentId,
    required this.courseId,
  });

  @override
  List<Object> get props => [studentId, courseId];

  @override
  String toString() => 'UnenrollStudentFromCourseEvent { studentId: $studentId, courseId: $courseId }';
}

/// Event to update student GPA
class UpdateStudentGPAEvent extends StudentEvent {
  /// Student ID to update GPA
  final String studentId;
  
  /// New GPA value
  final double gpa;

  const UpdateStudentGPAEvent({
    required this.studentId,
    required this.gpa,
  });

  @override
  List<Object> get props => [studentId, gpa];

  @override
  String toString() => 'UpdateStudentGPAEvent { studentId: $studentId, gpa: $gpa }';
}

/// Event to update student profile photo
class UpdateStudentProfilePhotoEvent extends StudentEvent {
  /// Student ID to update photo
  final String studentId;
  
  /// New profile photo URL
  final String photoUrl;

  const UpdateStudentProfilePhotoEvent({
    required this.studentId,
    required this.photoUrl,
  });

  @override
  List<Object> get props => [studentId, photoUrl];

  @override
  String toString() => 'UpdateStudentProfilePhotoEvent { studentId: $studentId, photoUrl: $photoUrl }';
}

/// Event to get student by ID
class GetStudentByIdEvent extends StudentEvent {
  /// Student ID to fetch
  final String studentId;

  const GetStudentByIdEvent(this.studentId);

  @override
  List<Object> get props => [studentId];

  @override
  String toString() => 'GetStudentByIdEvent { studentId: $studentId }';
}

/// Event to export students data
class ExportStudentsEvent extends StudentEvent {
  /// Export format (csv, excel, pdf)
  final String format;

  const ExportStudentsEvent(this.format);

  @override
  List<Object> get props => [format];

  @override
  String toString() => 'ExportStudentsEvent { format: $format }';
}

/// Event to generate ID card for a student
class GenerateIDCardEvent extends StudentEvent {
  /// Student ID to generate card for
  final String studentId;

  /// ID card template to use
  final IDCardTemplate template;

  /// Export format for the ID card
  final ExportFormat exportFormat;

  /// Custom file path (optional)
  final String? customPath;

  const GenerateIDCardEvent({
    required this.studentId,
    required this.template,
    required this.exportFormat,
    this.customPath,
  });

  @override
  List<Object?> get props => [studentId, template, exportFormat, customPath];

  @override
  String toString() => 'GenerateIDCardEvent { studentId: $studentId, template: ${template.name}, format: ${exportFormat.name} }';
}

/// Event to batch generate ID cards for multiple students
class BatchGenerateIDCardsEvent extends StudentEvent {
  /// List of student IDs to generate cards for
  final List<String> studentIds;

  /// ID card template to use
  final IDCardTemplate template;

  /// Export format for the ID cards
  final ExportFormat exportFormat;

  /// Custom file path (optional)
  final String? customPath;

  const BatchGenerateIDCardsEvent({
    required this.studentIds,
    required this.template,
    required this.exportFormat,
    this.customPath,
  });

  @override
  List<Object?> get props => [studentIds, template, exportFormat, customPath];

  @override
  String toString() => 'BatchGenerateIDCardsEvent { studentCount: ${studentIds.length}, template: ${template.name}, format: ${exportFormat.name} }';
}

/// Event to preview ID card before generation
class PreviewIDCardEvent extends StudentEvent {
  /// Student ID to preview card for
  final String studentId;

  /// ID card template to use
  final IDCardTemplate template;

  const PreviewIDCardEvent({
    required this.studentId,
    required this.template,
  });

  @override
  List<Object> get props => [studentId, template];

  @override
  String toString() => 'PreviewIDCardEvent { studentId: $studentId, template: ${template.name} }';
}

/// Event to import students data
class ImportStudentsEvent extends StudentEvent {
  /// File path or data to import
  final String filePath;

  const ImportStudentsEvent(this.filePath);

  @override
  List<Object> get props => [filePath];

  @override
  String toString() => 'ImportStudentsEvent { filePath: $filePath }';
}

/// Event to send notification to student
class SendNotificationToStudentEvent extends StudentEvent {
  /// Student ID to send notification to
  final String studentId;
  
  /// Notification message
  final String message;
  
  /// Notification title
  final String title;

  const SendNotificationToStudentEvent({
    required this.studentId,
    required this.message,
    required this.title,
  });

  @override
  List<Object> get props => [studentId, message, title];

  @override
  String toString() => 'SendNotificationToStudentEvent { studentId: $studentId, title: $title }';
}

/// Event to bulk update students
class BulkUpdateStudentsEvent extends StudentEvent {
  /// List of student IDs to update
  final List<String> studentIds;
  
  /// Update data
  final Map<String, dynamic> updateData;

  const BulkUpdateStudentsEvent({
    required this.studentIds,
    required this.updateData,
  });

  @override
  List<Object> get props => [studentIds, updateData];

  @override
  String toString() => 'BulkUpdateStudentsEvent { studentIds: ${studentIds.length}, updateData: $updateData }';
}

/// Event to generate student report
class GenerateStudentReportEvent extends StudentEvent {
  /// Student ID to generate report for
  final String studentId;
  
  /// Report type (academic, attendance, etc.)
  final String reportType;

  const GenerateStudentReportEvent({
    required this.studentId,
    required this.reportType,
  });

  @override
  List<Object> get props => [studentId, reportType];

  @override
  String toString() => 'GenerateStudentReportEvent { studentId: $studentId, reportType: $reportType }';
}
