import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/course_model.dart';
import '../blocs/course/course_bloc.dart';
import '../blocs/course/course_event.dart';

/// Edit Course Screen for modifying existing course information
///
/// This screen provides:
/// - Pre-populated form fields with existing course data
/// - Comprehensive course editing capabilities
/// - Beautiful Material Design 3 UI with animations
/// - Form validation and error handling
/// - Integration with CourseBloc for state management
class EditCourseScreen extends StatefulWidget {
  final Course course;

  const EditCourseScreen({
    super.key,
    required this.course,
  });

  @override
  State<EditCourseScreen> createState() => _EditCourseScreenState();
}

class _EditCourseScreenState extends State<EditCourseScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Form controllers
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _instructorController;
  late TextEditingController _imageUrlController;
  late TextEditingController _priceController;
  late TextEditingController _originalPriceController;
  late TextEditingController _durationController;
  late TextEditingController _lessonsCountController;
  late TextEditingController _enrolledStudentsController;
  late TextEditingController _ratingController;
  late TextEditingController _reviewCountController;
  late TextEditingController _tagsController;
  late TextEditingController _learningOutcomesController;
  late TextEditingController _syllabusController;

  // Form state
  String _selectedCategory = '';
  String _selectedLevel = '';
  bool _isFeatured = false;
  bool _isPopular = false;
  bool _isTrending = false;

  // Categories and levels
  final List<String> _categories = [
    'Programming',
    'Design',
    'Business',
    'Marketing',
    'Photography',
    'Music',
    'Health & Fitness',
    'Language',
    'Other',
  ];

  final List<String> _levels = [
    'Beginner',
    'Intermediate',
    'Advanced',
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
  }

  void _initializeControllers() {
    _titleController = TextEditingController(text: widget.course.title);
    _descriptionController = TextEditingController(text: widget.course.description);
    _instructorController = TextEditingController(text: widget.course.instructor);
    _imageUrlController = TextEditingController(text: widget.course.imageUrl);
    _priceController = TextEditingController(text: widget.course.price.toString());
    _originalPriceController = TextEditingController(
      text: widget.course.originalPrice?.toString() ?? '',
    );
    _durationController = TextEditingController(text: widget.course.duration);
    _lessonsCountController = TextEditingController(
      text: widget.course.lessonsCount?.toString() ?? '',
    );
    _enrolledStudentsController = TextEditingController(
      text: widget.course.enrolledStudents?.toString() ?? '',
    );
    _ratingController = TextEditingController(text: widget.course.rating.toString());
    _reviewCountController = TextEditingController(
      text: widget.course.reviewCount?.toString() ?? '',
    );
    _tagsController = TextEditingController(
      text: widget.course.tags?.join(', ') ?? '',
    );
    _learningOutcomesController = TextEditingController(
      text: widget.course.learningOutcomes.join('\n'),
    );
    _syllabusController = TextEditingController(
      text: widget.course.syllabus?.join('\n') ?? '',
    );

    // Initialize form state
    _selectedCategory = widget.course.category;
    _selectedLevel = widget.course.level;
    _isFeatured = widget.course.isFeatured;
    _isPopular = widget.course.isPopular;
    _isTrending = widget.course.isTrending;
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _instructorController.dispose();
    _imageUrlController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _durationController.dispose();
    _lessonsCountController.dispose();
    _enrolledStudentsController.dispose();
    _ratingController.dispose();
    _reviewCountController.dispose();
    _tagsController.dispose();
    _learningOutcomesController.dispose();
    _syllabusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Edit Course',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        TextButton(
          onPressed: _saveCourse,
          child: const Text(
            'SAVE',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildPricingSection(),
              const SizedBox(height: 24),
              _buildCategorySection(),
              const SizedBox(height: 24),
              _buildStatusSection(),
              const SizedBox(height: 24),
              _buildContentSection(),
              const SizedBox(height: 100), // Space for FAB
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Basic Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Course Title *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a course title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Course Description *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a course description';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _instructorController,
              decoration: const InputDecoration(
                labelText: 'Instructor Name *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter instructor name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _imageUrlController,
              decoration: const InputDecoration(
                labelText: 'Course Image URL',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.image),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _durationController,
              decoration: const InputDecoration(
                labelText: 'Duration (e.g., "10 hours", "3 weeks") *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.access_time),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter course duration';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.green.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Pricing & Statistics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'Price (\$) *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter price';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Please enter valid price';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _originalPriceController,
                    decoration: const InputDecoration(
                      labelText: 'Original Price (\$)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.money_off),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _ratingController,
                    decoration: const InputDecoration(
                      labelText: 'Rating (0-5)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.star),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _reviewCountController,
                    decoration: const InputDecoration(
                      labelText: 'Review Count',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.reviews),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _lessonsCountController,
                    decoration: const InputDecoration(
                      labelText: 'Lessons Count',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.play_lesson),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _enrolledStudentsController,
                    decoration: const InputDecoration(
                      labelText: 'Enrolled Students',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.people),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.category, color: Colors.purple.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Category & Level',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedCategory.isNotEmpty ? _selectedCategory : null,
                    decoration: const InputDecoration(
                      labelText: 'Category *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    items: _categories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value ?? '';
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a category';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedLevel.isNotEmpty ? _selectedLevel : null,
                    decoration: const InputDecoration(
                      labelText: 'Level *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.trending_up),
                    ),
                    items: _levels.map((level) {
                      return DropdownMenuItem(
                        value: level,
                        child: Text(level),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedLevel = value ?? '';
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a level';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _tagsController,
              decoration: const InputDecoration(
                labelText: 'Tags (comma separated)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.tag),
                hintText: 'e.g., programming, web development, javascript',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flag, color: Colors.orange.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Course Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Featured Course'),
              subtitle: const Text('Show this course in featured section'),
              value: _isFeatured,
              onChanged: (value) {
                setState(() {
                  _isFeatured = value;
                });
              },
              secondary: const Icon(Icons.star),
            ),
            SwitchListTile(
              title: const Text('Popular Course'),
              subtitle: const Text('Mark this course as popular'),
              value: _isPopular,
              onChanged: (value) {
                setState(() {
                  _isPopular = value;
                });
              },
              secondary: const Icon(Icons.trending_up),
            ),
            SwitchListTile(
              title: const Text('Trending Course'),
              subtitle: const Text('Show this course in trending section'),
              value: _isTrending,
              onChanged: (value) {
                setState(() {
                  _isTrending = value;
                });
              },
              secondary: const Icon(Icons.whatshot),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.library_books, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'Course Content',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _learningOutcomesController,
              decoration: const InputDecoration(
                labelText: 'Learning Outcomes (one per line)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.check_circle),
                hintText: 'Enter each learning outcome on a new line',
              ),
              maxLines: 5,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _syllabusController,
              decoration: const InputDecoration(
                labelText: 'Course Syllabus (one item per line)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.list_alt),
                hintText: 'Enter each syllabus item on a new line',
              ),
              maxLines: 5,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _saveCourse,
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.save),
      label: const Text('Save Changes'),
    );
  }

  void _saveCourse() {
    if (_formKey.currentState!.validate()) {
      try {
        final updatedCourse = Course(
          id: widget.course.id,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          instructor: _instructorController.text.trim(),
          imageUrl: _imageUrlController.text.trim(),
          rating: double.tryParse(_ratingController.text) ?? 0.0,
          reviewCount: _reviewCountController.text.isNotEmpty
            ? int.tryParse(_reviewCountController.text)
            : null,
          price: double.parse(_priceController.text),
          originalPrice: _originalPriceController.text.isNotEmpty
            ? double.tryParse(_originalPriceController.text)
            : null,
          duration: _durationController.text.trim(),
          lessonsCount: _lessonsCountController.text.isNotEmpty
            ? int.tryParse(_lessonsCountController.text)
            : null,
          level: _selectedLevel,
          tags: _tagsController.text.isNotEmpty
            ? _tagsController.text.split(',').map((e) => e.trim()).toList()
            : null,
          isFeatured: _isFeatured,
          isPopular: _isPopular,
          isTrending: _isTrending,
          createdAt: widget.course.createdAt,
          updatedAt: DateTime.now(),
          category: _selectedCategory,
          learningOutcomes: _learningOutcomesController.text.isNotEmpty
            ? _learningOutcomesController.text.split('\n')
                .map((e) => e.trim())
                .where((e) => e.isNotEmpty)
                .toList()
            : [],
          modules: widget.course.modules, // Keep existing modules
          progress: widget.course.progress,
          isEnrolled: widget.course.isEnrolled,
          certificateUrl: widget.course.certificateUrl,
          syllabus: _syllabusController.text.isNotEmpty
            ? _syllabusController.text.split('\n')
                .map((e) => e.trim())
                .where((e) => e.isNotEmpty)
                .toList()
            : null,
          enrolledStudents: _enrolledStudentsController.text.isNotEmpty
            ? double.tryParse(_enrolledStudentsController.text)
            : null,
        );

        context.read<CourseBloc>().add(UpdateCourseEvent(updatedCourse));

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Course updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating course: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
