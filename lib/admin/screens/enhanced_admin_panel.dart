import 'package:flutter/material.dart';
import 'package:passion_admin_app/admin/screens/student_list_screen.dart';
import '../widgets/beautiful_animated_sidebar_clean.dart';
import '../utils/responsive_utils.dart';
import 'beautiful_animated_dashboard.dart';
import 'course_screen.dart';
import 'users_screen.dart';
import 'fee_management_screen.dart';
import 'exam_management_screen.dart';
import 'payment_screen.dart';
import 'support_screen.dart';
import 'notifications_screen.dart';
import 'settings_screen.dart';

class EnhancedAdminPanel extends StatefulWidget {
  const EnhancedAdminPanel({super.key});

  @override
  State<EnhancedAdminPanel> createState() => _EnhancedAdminPanelState();
}

class _EnhancedAdminPanelState extends State<EnhancedAdminPanel> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const BeautifulAnimatedDashboard() ,
    const CourseScreen(),
    const StudentListScreen(),
    const FeeManagementScreen(),
    const PaymentScreen(),
    const SupportScreen(),
    const NotificationsScreen(),
    const SettingsScreen(),
  ];

  void _onItemSelected(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: context.shouldUseDrawer ? _buildAppBar() : null,
      drawer: context.shouldUseDrawer ? _buildDrawer() : null,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade50,
              Colors.purple.shade50,
              Colors.pink.shade50,
            ],
          ),
        ),
        child: context.shouldUseDrawer
            ? _screens[_selectedIndex]
            : Row(
                children: [
                  // Fixed width sidebar
                  Container(
                    width: context.sidebarWidth,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(2, 0),
                        ),
                      ],
                    ),
                    child: BeautifulAnimatedSidebar(
                      selectedIndex: _selectedIndex,
                      onItemSelected: _onItemSelected,
                      isDrawer: false,
                    ),
                  ),
                  // Main content area
                  Expanded(
                    child: _screens[_selectedIndex],
                  ),
                ],
              ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Passion Admin'),
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          onPressed: () {},
          icon: const Icon(Icons.notifications_outlined),
        ),
        const CircleAvatar(
          backgroundColor: Colors.white24,
          child: Icon(Icons.person, color: Colors.white),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: BeautifulAnimatedSidebar(
        selectedIndex: _selectedIndex,
        onItemSelected: (index) {
          _onItemSelected(index);
          Navigator.pop(context);
        },
        isDrawer: true,
      ),
    );
  }
}
