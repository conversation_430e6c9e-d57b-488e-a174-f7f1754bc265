import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/student/student_bloc.dart';
import '../blocs/student/student_event.dart';
import '../blocs/student/student_state.dart';
import '../models/student_model.dart';
import '../repositories/student_repository.dart';
import '../utils/responsive_utils.dart';
import 'create_student_screen.dart';
import 'student_detail_screen.dart';
import 'edit_student_screen.dart';

/// Student List Screen for displaying and managing students
/// 
/// This screen provides:
/// - Student list with search and filter capabilities
/// - Real-time data updates using BLoC pattern
/// - Responsive design for all screen sizes
/// - CRUD operations for student management
/// - Beautiful UI with animations and transitions
class StudentListScreen extends StatefulWidget {
  const StudentListScreen({super.key});

  @override
  State<StudentListScreen> createState() => _StudentListScreenState();
}

class _StudentListScreenState extends State<StudentListScreen>
    with TickerProviderStateMixin {

  /// StudentBloc instance from context
  late StudentBloc _studentBloc;

  /// Animation controller for list animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  /// Search controller
  final _searchController = TextEditingController();

  /// Current filter state
  String? _selectedStatus;
  String? _selectedGradeLevel;
  bool _showActiveOnly = false;

  @override
  void initState() {
    super.initState();
    _studentBloc = context.read<StudentBloc>();
    _studentBloc.add(const LoadStudentsEvent()); // Load students on init
    _initializeAnimations();
  }

  /// Initialize animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _studentBloc.close();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _studentBloc,
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  /// Build the app bar with search and filter options
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Student Management',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      foregroundColor: Theme.of(context).primaryColor,
      actions: [
        // Refresh button
        IconButton(
          onPressed: () {
            _studentBloc.add(const RefreshStudentsEvent());
          },
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
        
        // Filter button
        IconButton(
          onPressed: _showFilterDialog,
          icon: const Icon(Icons.filter_list),
          tooltip: 'Filter Students',
        ),
        
        // More options
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('Export Data'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload),
                  SizedBox(width: 8),
                  Text('Import Data'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'analytics',
              child: Row(
                children: [
                  Icon(Icons.analytics),
                  SizedBox(width: 8),
                  Text('Analytics'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with stats
            _buildHeader(),
            const SizedBox(height: 24),
            
            // Search bar
            _buildSearchBar(),
            const SizedBox(height: 16),
            
            // Filter chips
            _buildFilterChips(),
            const SizedBox(height: 16),
            
            // Student list
            Expanded(
              child: _buildStudentList(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build header with statistics
  Widget _buildHeader() {
    return BlocBuilder<StudentBloc, StudentState>(
      builder: (context, state) {
        if (state is StudentLoaded) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Students Overview',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              
              // Statistics cards
              if (context.isLargeScreen) ...[
                Row(
                  children: [
                    Expanded(child: _buildStatCard('Total Students', state.totalStudents, Icons.people, Colors.blue)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard('Active', state.activeStudents, Icons.check_circle, Colors.green)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard('Inactive', state.inactiveStudents, Icons.pause_circle, Colors.orange)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard('Filtered', state.displayStudents.length, Icons.filter_list, Colors.purple)),
                  ],
                ),
              ] else ...[
                // Mobile layout - 2x2 grid
                Row(
                  children: [
                    Expanded(child: _buildStatCard('Total', state.totalStudents, Icons.people, Colors.blue)),
                    const SizedBox(width: 12),
                    Expanded(child: _buildStatCard('Active', state.activeStudents, Icons.check_circle, Colors.green)),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(child: _buildStatCard('Inactive', state.inactiveStudents, Icons.pause_circle, Colors.orange)),
                    const SizedBox(width: 12),
                    Expanded(child: _buildStatCard('Filtered', state.displayStudents.length, Icons.filter_list, Colors.purple)),
                  ],
                ),
              ],
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// Build statistics card
  Widget _buildStatCard(String title, int count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Build search bar
  Widget _buildSearchBar() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Search students by name, email, ID, or major...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: (_searchController.text.isNotEmpty)
            ? IconButton(
                onPressed: () {
                  _searchController.clear();
                  _studentBloc.add(const ClearSearchEvent());
                },
                icon: const Icon(Icons.clear),
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      onChanged: (query) {
        // Debounce search to avoid excessive API calls
        Future.delayed(const Duration(milliseconds: 500), () {
          if (_searchController.text == query) {
            if (query.trim().isEmpty) {
              _studentBloc.add(const ClearSearchEvent());
            } else {
              _studentBloc.add(SearchStudentsEvent(query));
            }
          }
        });
      },
    );
  }

  /// Build filter chips
  Widget _buildFilterChips() {
    return BlocBuilder<StudentBloc, StudentState>(
      builder: (context, state) {
        if (state is StudentLoaded && state.hasFiltersOrSearch) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade600, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Showing ${state.displayStudents.length} of ${state.totalStudents} students',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    _searchController.clear();
                    _selectedStatus = null;
                    _selectedGradeLevel = null;
                    _showActiveOnly = false;
                    _studentBloc.add(const ClearSearchEvent());
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// Build student list
  Widget _buildStudentList() {
    return BlocBuilder<StudentBloc, StudentState>(
      builder: (context, state) {
        if (state is StudentLoading) {
          return _buildLoadingState();
        } else if (state is StudentLoaded) {
          if (state.displayStudents.isEmpty) {
            return _buildEmptyState();
          }
          return _buildStudentGrid(state.displayStudents);
        } else if (state is StudentError) {
          return _buildErrorState(state);
        }
        return _buildLoadingState();
      },
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading students...'),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No Students Found',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first student to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToCreateStudent,
            icon: const Icon(Icons.add),
            label: const Text('Add Student'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(StudentError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Students',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              state.message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              _studentBloc.add(const LoadStudentsEvent());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build student grid/list
  Widget _buildStudentGrid(List<Student> students) {
    if (context.isLargeScreen) {
      // Desktop/tablet - grid layout
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: context.isLargeScreen ? 3 : 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: students.length,
        itemBuilder: (context, index) => _buildStudentCard(students[index]),
      );
    } else {
      // Mobile - list layout
      return ListView.builder(
        itemCount: students.length,
        itemBuilder: (context, index) => _buildStudentListItem(students[index]),
      );
    }
  }

  /// Build student card for grid layout
  Widget _buildStudentCard(Student student) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _viewStudentDetails(student),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with photo and status
              Row(
                children: [
                  // Profile photo
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: student.profilePhotoUrl?.isNotEmpty == true
                        ? NetworkImage(student.profilePhotoUrl!)
                        : null,
                    child: student.profilePhotoUrl?.isEmpty != false
                        ? Text(
                            student.fullName.isNotEmpty
                                ? student.fullName[0].toUpperCase()
                                : '?',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),

                  // Status badge
                  Expanded(
                    child: _buildStatusBadge(student.status),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Student info
              Text(
                student.fullName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              Text(
                student.studentId,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(height: 4),

              Text(
                student.email,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const Spacer(),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      icon: Icons.edit,
                      label: 'Edit',
                      onPressed: () => _editStudent(student),
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildActionButton(
                      icon: Icons.delete,
                      label: 'Delete',
                      onPressed: () => _deleteStudent(student),
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build student list item for mobile layout
  Widget _buildStudentListItem(Student student) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: student.profilePhotoUrl?.isNotEmpty == true
              ? NetworkImage(student.profilePhotoUrl!)
              : null,
          child: student.profilePhotoUrl?.isEmpty != false
              ? Text(
                  student.fullName.isNotEmpty
                      ? student.fullName[0].toUpperCase()
                      : '?',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )
              : null,
        ),
        title: Text(
          student.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(student.studentId),
            Text(
              student.email,
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusBadge(student.status),
            PopupMenuButton<String>(
              onSelected: (action) => _handleStudentAction(action, student),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: Row(
                    children: [
                      Icon(Icons.visibility),
                      SizedBox(width: 8),
                      Text('View Details'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _viewStudentDetails(student),
      ),
    );
  }

  /// Build status badge
  Widget _buildStatusBadge(StudentStatus status) {
    Color color;
    switch (status) {
      case StudentStatus.active:
        color = Colors.green;
        break;
      case StudentStatus.inactive:
        color = Colors.orange;
        break;
      case StudentStatus.suspended:
        color = Colors.red;
        break;
      case StudentStatus.graduated:
        color = Colors.blue;
        break;
      case StudentStatus.transferred:
        color = Colors.purple;
        break;
      case StudentStatus.dropped:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build action button
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color.withOpacity(0.5)),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
    );
  }

  /// Build floating action button
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToCreateStudent,
      icon: const Icon(Icons.add),
      label: const Text('Add Student'),
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
    );
  }

  /// Navigate to create student screen
  void _navigateToCreateStudent() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateStudentScreen(),
      ),
    );

    if (result == true) {
      // Refresh students when returning from create screen
      _studentBloc.add(const RefreshStudentsEvent());
    }
  }

  /// View student details
  void _viewStudentDetails(Student student) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StudentDetailScreen(student: student),
      ),
    );

    // Refresh list if student was updated or deleted
    if (result == true) {
      _studentBloc.add(LoadStudentsEvent());
    }
  }

  /// Edit student
  void _editStudent(Student student) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditStudentScreen(student: student),
      ),
    );

    // Refresh list if student was updated
    if (result == true) {
      _studentBloc.add(LoadStudentsEvent());
    }
  }

  /// Delete student with confirmation
  void _deleteStudent(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Student'),
        content: Text(
          'Are you sure you want to delete ${student.fullName}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _studentBloc.add(DeleteStudentEvent(student.id));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Handle student action from popup menu
  void _handleStudentAction(String action, Student student) {
    switch (action) {
      case 'view':
        _viewStudentDetails(student);
        break;
      case 'edit':
        _editStudent(student);
        break;
      case 'delete':
        _deleteStudent(student);
        break;
    }
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportStudents();
        break;
      case 'import':
        _importStudents();
        break;
      case 'analytics':
        _showAnalytics();
        break;
    }
  }

  /// Export students data
  void _exportStudents() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export feature coming soon!')),
    );
  }

  /// Import students data
  void _importStudents() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import feature coming soon!')),
    );
  }

  /// Show analytics
  void _showAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Analytics feature coming soon!')),
    );
  }

  /// Show filter dialog
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Students'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status filter
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'Status',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Statuses')),
                ...StudentStatus.values.map(
                  (status) => DropdownMenuItem(
                    value: status.name,
                    child: Text(status.displayName),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Active only filter
            CheckboxListTile(
              title: const Text('Active Students Only'),
              value: _showActiveOnly,
              onChanged: (value) {
                setState(() {
                  _showActiveOnly = value ?? false;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _applyFilters();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  /// Apply filters
  void _applyFilters() {
    if (_selectedStatus != null) {
      final status = StudentStatus.values.firstWhere(
        (s) => s.name == _selectedStatus,
      );
      _studentBloc.add(FilterStudentsByStatusEvent(status));
    } else if (_showActiveOnly) {
      _studentBloc.add(const LoadActiveStudentsEvent());
    } else {
      _studentBloc.add(const LoadStudentsEvent());
    }
  }
}
