import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/course/course_bloc.dart';
import '../blocs/course/course_event.dart';
import '../blocs/course/course_state.dart';
import '../models/course_model.dart';
import '../repositories/course_repository.dart';
import 'create_course_screen.dart';
import 'course_detail_screen.dart';
import 'edit_course_screen.dart';

/// Screen for displaying and managing courses
/// 
/// This screen provides:
/// - List of all courses with search and filter capabilities
/// - Course management actions (edit, delete, toggle publication)
/// - Beautiful UI with animations and responsive design
/// - Integration with CourseBloc for state management
/// - Real-time updates from Firebase
class CourseListScreen extends StatefulWidget {
  const CourseListScreen({super.key});

  @override
  State<CourseListScreen> createState() => _CourseListScreenState();
}

class _CourseListScreenState extends State<CourseListScreen>
    with TickerProviderStateMixin {

  /// CourseBloc instance
  late CourseBloc _courseBloc;

  /// Animation controller for list animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  /// Search controller
  final _searchController = TextEditingController();

  /// Current filter state
  String? _selectedCategory;
  bool _showOnlyPublished = false;

  @override
  void initState() {
    super.initState();
    _courseBloc = CourseBloc(courseRepository: CourseRepository());
    _courseBloc.add(const LoadCoursesEvent()); // Load courses on init
    _initializeAnimations();
  }

  /// Initialize animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _courseBloc.close();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _courseBloc,
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  /// Build the app bar with search and filter options
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Course Management',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade600,
              Colors.purple.shade600,
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: _showFilterDialog,
          icon: const Icon(Icons.filter_list),
          tooltip: 'Filter Courses',
        ),
        IconButton(
          onPressed: () {
            _courseBloc.add(const RefreshCoursesEvent());
          },
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
    );
  }

  /// Build the main body with course list
  Widget _buildBody() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.blue.shade50,
            Colors.purple.shade50,
            Colors.pink.shade50,
          ],
        ),
      ),
      child: Column(
        children: [
          // Search bar
          _buildSearchBar(),
          
          // Course list
          Expanded(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildCourseList(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build search bar
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (query) {
          // Debounce search to avoid too many API calls
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_searchController.text == query) {
              if (query.trim().isEmpty) {
                _courseBloc.add(const ClearSearchEvent());
              } else {
                _courseBloc.add(SearchCoursesEvent(query));
              }
            }
          });
        },
        decoration: InputDecoration(
          hintText: 'Search courses by title, instructor, or category...',
          prefixIcon: Icon(Icons.search, color: Colors.blue.shade600),
          suffixIcon: (_searchController.text.isNotEmpty)
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _courseBloc.add(const ClearSearchEvent());
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
      ),
    );
  }

  /// Build course list with BLoC state management
  Widget _buildCourseList() {
    return BlocConsumer<CourseBloc, CourseState>(
      listener: _handleStateChanges,
      builder: (context, state) {
        if (state is CourseLoading) {
          return _buildLoadingState();
        } else if (state is CourseLoaded) {
          return _buildLoadedState(state);
        } else if (state is CourseError) {
          return _buildErrorState(state);
        } else {
          return _buildEmptyState();
        }
      },
    );
  }

  /// Handle BLoC state changes
  void _handleStateChanges(BuildContext context, CourseState state) {
    if (state is CourseDeleted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (state is CourseError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () {
              _courseBloc.add(const LoadCoursesEvent());
            },
          ),
        ),
      );
    }
  }

  /// Build loading state UI
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading courses...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// Build loaded state UI with course list
  Widget _buildLoadedState(CourseLoaded state) {
    final courses = state.filteredCourses;
    
    if (courses.isEmpty) {
      return _buildEmptyCoursesState(state);
    }

    return RefreshIndicator(
      onRefresh: () async {
        _courseBloc.add(const RefreshCoursesEvent());
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: courses.length,
        itemBuilder: (context, index) {
          final course = courses[index];
          return _buildCourseCard(course, index);
        },
      ),
    );
  }

  /// Build empty courses state with filters applied
  Widget _buildEmptyCoursesState(CourseLoaded state) {
    String message = 'No courses found';
    if (state.hasActiveFilters) {
      message = 'No courses match your current filters';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try adjusting your search or filters',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          if (state.hasActiveFilters) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _selectedCategory = null;
                  _showOnlyPublished = false;
                });
                _courseBloc.add(const ClearSearchEvent());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
              ),
              child: const Text('Clear Filters'),
            ),
          ],
        ],
      ),
    );
  }

  /// Build individual course card
  Widget _buildCourseCard(Course course, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.blue.shade50,
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Course header
                _buildCourseHeader(course),

                const SizedBox(height: 16),

                // Course details
                _buildCourseDetails(course),

                const SizedBox(height: 16),

                // Course tags
                if (course.tags?.isNotEmpty == true) _buildCourseTags(course),

                const SizedBox(height: 16),

                // Course actions
                _buildCourseActions(course),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build course header with title and status
  Widget _buildCourseHeader(Course course) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Course image or placeholder
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.blue.shade100,
          ),
          child: course.imageUrl.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    course.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildCoursePlaceholder();
                    },
                  ),
                )
              : _buildCoursePlaceholder(),
        ),

        const SizedBox(width: 16),

        // Course info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      course.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  _buildPublicationStatus(course),
                ],
              ),

              const SizedBox(height: 4),

              Text(
                'by ${course.instructor}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                course.description,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build course placeholder icon
  Widget _buildCoursePlaceholder() {
    return Icon(
      Icons.school,
      size: 40,
      color: Colors.blue.shade600,
    );
  }

  /// Build publication status badge
  Widget _buildPublicationStatus(Course course) {
    return Wrap(
      spacing: 4,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '⭐ ${course.rating.toStringAsFixed(1)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (course.reviewCount != null && course.reviewCount! > 0)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${course.reviewCount} reviews',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  /// Build course details row
  Widget _buildCourseDetails(Course course) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildDetailChip(
          icon: Icons.category,
          label: course.category,
          color: Colors.purple,
        ),
        _buildDetailChip(
          icon: Icons.trending_up,
          label: course.level,
          color: Colors.orange,
        ),
        _buildDetailChip(
          icon: Icons.access_time,
          label: course.duration,
          color: Colors.green,
        ),
        _buildDetailChip(
          icon: Icons.attach_money,
          label: '\$${course.price.toStringAsFixed(2)}',
          color: Colors.blue,
        ),
        if (course.lessonsCount != null)
          _buildDetailChip(
            icon: Icons.list,
            label: '${course.lessonsCount} lessons',
            color: Colors.teal,
          ),
        if (course.rating > 0)
          _buildDetailChip(
            icon: Icons.star,
            label: '${course.rating.toStringAsFixed(1)}',
            color: Colors.amber,
          ),
        if (course.isFeatured)
          _buildDetailChip(
            icon: Icons.star,
            label: 'Featured',
            color: Colors.orange,
          ),
        if (course.isPopular)
          _buildDetailChip(
            icon: Icons.trending_up,
            label: 'Popular',
            color: Colors.green,
          ),
        if (course.isTrending)
          _buildDetailChip(
            icon: Icons.whatshot,
            label: 'Trending',
            color: Colors.red,
          ),
      ],
    );
  }

  /// Build detail chip
  Widget _buildDetailChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build course tags
  Widget _buildCourseTags(Course course) {
    if (course.tags == null || course.tags!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: course.tags!.take(3).map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tag,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build course action buttons
  Widget _buildCourseActions(Course course) {
    return Row(
      children: [
        // Student count and enrollment info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (course.enrolledStudents != null)
                Row(
                  children: [
                    Icon(Icons.people, size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Text(
                      '${course.enrolledStudents!.toInt()} enrolled',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              if (course.isEnrolled)
                Row(
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.green.shade600),
                    const SizedBox(width: 4),
                    Text(
                      'Enrolled',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green.shade600,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // Action buttons
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // View course details
            IconButton(
              onPressed: () => _viewCourseDetails(course),
              icon: Icon(Icons.visibility, color: Colors.blue.shade600),
              tooltip: 'View Details',
            ),

            // Toggle featured status
            IconButton(
              onPressed: () => _toggleFeaturedStatus(course),
              icon: Icon(
                course.isFeatured ? Icons.star : Icons.star_border,
                color: course.isFeatured ? Colors.amber : Colors.grey,
              ),
              tooltip: course.isFeatured ? 'Remove from Featured' : 'Mark as Featured',
            ),

            // Edit course
            IconButton(
              onPressed: () => _editCourse(course),
              icon: Icon(Icons.edit, color: Colors.green.shade600),
              tooltip: 'Edit Course',
            ),

            // Delete course
            IconButton(
              onPressed: () => _showDeleteConfirmation(course),
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete Course',
            ),
          ],
        ),
      ],
    );
  }

  /// Build floating action button
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CreateCourseScreen(),
          ),
        ).then((_) {
          // Refresh courses when returning from create screen
          _courseBloc.add(const RefreshCoursesEvent());
        });
      },
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('Create Course'),
    );
  }

  /// Show filter dialog
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Courses'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Category filter
            DropdownButtonFormField<String?>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('All Categories'),
                ),
                ...CourseCategory.values.map((category) {
                  return DropdownMenuItem<String?>(
                    value: category.displayName,
                    child: Text(category.displayName),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Featured filter
            CheckboxListTile(
              title: const Text('Featured Only'),
              value: _showOnlyPublished,
              onChanged: (value) {
                setState(() {
                  _showOnlyPublished = value ?? false;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _applyFilters();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  /// Apply selected filters
  void _applyFilters() {
    if (_selectedCategory != null) {
      _courseBloc.add(
        FilterCoursesByCategoryEvent(_selectedCategory!),
      );
    } else if (_showOnlyPublished) {
      _courseBloc.add(const LoadPublishedCoursesEvent());
    } else {
      _courseBloc.add(const LoadCoursesEvent());
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(Course course) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Course'),
        content: Text(
          'Are you sure you want to delete "${course.title}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _courseBloc.add(DeleteCourseEvent(course.id));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Navigate to course detail screen
  void _viewCourseDetails(Course course) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: _courseBloc,
          child: CourseDetailScreen(course: course),
        ),
      ),
    );
  }

  /// Navigate to edit course screen
  void _editCourse(Course course) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: _courseBloc,
          child: EditCourseScreen(course: course),
        ),
      ),
    );
  }

  /// Toggle featured status of a course
  void _toggleFeaturedStatus(Course course) {
    final updatedCourse = Course(
      id: course.id,
      title: course.title,
      description: course.description,
      instructor: course.instructor,
      imageUrl: course.imageUrl,
      rating: course.rating,
      reviewCount: course.reviewCount,
      price: course.price,
      originalPrice: course.originalPrice,
      duration: course.duration,
      lessonsCount: course.lessonsCount,
      level: course.level,
      tags: course.tags,
      isFeatured: !course.isFeatured, // Toggle featured status
      isPopular: course.isPopular,
      isTrending: course.isTrending,
      createdAt: course.createdAt,
      updatedAt: DateTime.now(),
      category: course.category,
      learningOutcomes: course.learningOutcomes,
      modules: course.modules,
      progress: course.progress,
      isEnrolled: course.isEnrolled,
      certificateUrl: course.certificateUrl,
      syllabus: course.syllabus,
      enrolledStudents: course.enrolledStudents,
    );

    _courseBloc.add(UpdateCourseEvent(updatedCourse));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          course.isFeatured
            ? 'Course removed from featured'
            : 'Course marked as featured',
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

  /// Build error state UI
  Widget _buildErrorState(CourseError state) {
    return BlocBuilder<CourseBloc, CourseState>(
      builder: (context, currentState) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                'Error Loading Courses',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.read<CourseBloc>().add(const LoadCoursesEvent());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build empty state UI
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No Courses Yet',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Create your first course to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
