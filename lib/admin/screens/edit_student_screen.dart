import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/student/student_bloc.dart';
import '../blocs/student/student_event.dart';
import '../blocs/student/student_state.dart';
import '../models/student_model.dart';
import '../repositories/student_repository.dart';
import '../utils/responsive_utils.dart';

/// Edit Student Screen for modifying existing student information
/// 
/// This screen provides:
/// - Pre-filled form with existing student data
/// - Comprehensive editing capabilities
/// - Real-time validation and error handling
/// - Beautiful responsive design with animations
/// - Integration with BLoC pattern for state management
class EditStudentScreen extends StatefulWidget {
  final Student student;

  const EditStudentScreen({
    super.key,
    required this.student,
  });

  @override
  State<EditStudentScreen> createState() => _EditStudentScreenState();
}

class _EditStudentScreenState extends State<EditStudentScreen>
    with TickerProviderStateMixin {
  
  /// StudentBloc instance
  late StudentBloc _studentBloc;
  
  /// Animation controller for page animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  /// Form key for validation
  final _formKey = GlobalKey<FormState>();
  
  /// Form controllers
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _majorController = TextEditingController();
  final _gradeLevelController = TextEditingController();
  final _gpaController = TextEditingController();
  final _creditsController = TextEditingController();
  
  /// Guardian info controllers
  final _guardianNameController = TextEditingController();
  final _guardianPhoneController = TextEditingController();
  final _guardianEmailController = TextEditingController();
  final _guardianRelationshipController = TextEditingController();
  
  /// Emergency contact controllers
  final _emergencyNameController = TextEditingController();
  final _emergencyPhoneController = TextEditingController();
  final _emergencyEmailController = TextEditingController();
  final _emergencyRelationshipController = TextEditingController();
  
  /// Form state variables
  DateTime? _selectedDateOfBirth;
  String? _selectedGender;
  StudentStatus _selectedStatus = StudentStatus.active;
  
  /// Loading state
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _studentBloc = BlocProvider.of<StudentBloc>(context);
    _initializeAnimations();
    _populateFormFields();
  }

  /// Initialize animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  /// Populate form fields with existing student data
  void _populateFormFields() {
    final student = widget.student;
    
    _fullNameController.text = student.fullName;
    _emailController.text = student.email;
    _phoneController.text = student.phoneNumber ?? '';
    _addressController.text = student.address ?? '';
    _majorController.text = student.major ?? '';
    _gradeLevelController.text = student.gradeLevel ?? '';
    _gpaController.text = student.gpa?.toString() ?? '';
    _creditsController.text = student.totalCredits.toString();
    
    _selectedDateOfBirth = student.dateOfBirth;
    _selectedGender = student.gender;
    _selectedStatus = student.status;
    
    // Guardian info
    if (student.guardianInfo != null) {
      _guardianNameController.text = student.guardianInfo!.name;
      _guardianPhoneController.text = student.guardianInfo!.phoneNumber ?? '';
      _guardianEmailController.text = student.guardianInfo!.email ?? '';
      _guardianRelationshipController.text = student.guardianInfo!.relationship ?? '';
    }
    
    // Emergency contact
    if (student.emergencyContact != null) {
      _emergencyNameController.text = student.emergencyContact!.name;
      _emergencyPhoneController.text = student.emergencyContact!.phoneNumber;
      _emergencyEmailController.text = student.emergencyContact!.email ?? '';
      _emergencyRelationshipController.text = student.emergencyContact!.relationship ?? '';
    }
  }

  @override
  void dispose() {
    _studentBloc.close();
    _animationController.dispose();
    _disposeControllers();
    super.dispose();
  }

  /// Dispose all text controllers
  void _disposeControllers() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _majorController.dispose();
    _gradeLevelController.dispose();
    _gpaController.dispose();
    _creditsController.dispose();
    _guardianNameController.dispose();
    _guardianPhoneController.dispose();
    _guardianEmailController.dispose();
    _guardianRelationshipController.dispose();
    _emergencyNameController.dispose();
    _emergencyPhoneController.dispose();
    _emergencyEmailController.dispose();
    _emergencyRelationshipController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _studentBloc,
      child: BlocListener<StudentBloc, StudentState>(
        listener: _handleStateChanges,
        child: Scaffold(
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  /// Handle BLoC state changes
  void _handleStateChanges(BuildContext context, StudentState state) {
    if (state is StudentUpdating) {
      setState(() {
        _isLoading = true;
      });
    } else if (state is StudentUpdated) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
      
      Navigator.pop(context, true); // Return with success flag
      
    } else if (state is StudentError) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${state.message}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _submitForm,
          ),
        ),
      );
    }
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Edit Student',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      foregroundColor: Theme.of(context).primaryColor,
      actions: [
        // Save button
        TextButton(
          onPressed: _isLoading ? null : _submitForm,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text(
                  'Save',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
        ),
      ],
    );
  }

  /// Build the main body content
  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: context.responsivePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: 24),
                
                // Form sections
                if (context.isLargeScreen) ...[
                  // Desktop layout - two columns
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Column(
                          children: [
                            _buildPersonalInfoSection(),
                            const SizedBox(height: 16),
                            _buildAcademicInfoSection(),
                          ],
                        ),
                      ),
                      const SizedBox(width: 24),
                      Expanded(
                        child: Column(
                          children: [
                            _buildGuardianInfoSection(),
                            const SizedBox(height: 16),
                            _buildEmergencyContactSection(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // Mobile layout - single column
                  _buildPersonalInfoSection(),
                  const SizedBox(height: 16),
                  _buildAcademicInfoSection(),
                  const SizedBox(height: 16),
                  _buildGuardianInfoSection(),
                  const SizedBox(height: 16),
                  _buildEmergencyContactSection(),
                ],
                
                const SizedBox(height: 32),
                
                // Action buttons
                _buildActionButtons(),
                
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build header section
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.shade600,
            Colors.orange.shade400,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.edit,
            color: Colors.white,
            size: context.isLargeScreen ? 32 : 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Edit Student Information',
                  style: TextStyle(
                    fontSize: context.isLargeScreen ? 24 : 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Update ${widget.student.fullName}\'s details',
                  style: TextStyle(
                    fontSize: context.isLargeScreen ? 16 : 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build personal information section
  Widget _buildPersonalInfoSection() {
    return _buildFormSection(
      title: 'Personal Information',
      icon: Icons.person,
      children: [
        // Full Name
        TextFormField(
          controller: _fullNameController,
          decoration: const InputDecoration(
            labelText: 'Full Name *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
          validator: (value) {
            if (value?.trim().isEmpty ?? true) {
              return 'Full name is required';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // Email
        TextFormField(
          controller: _emailController,
          decoration: const InputDecoration(
            labelText: 'Email Address *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.email),
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value?.trim().isEmpty ?? true) {
              return 'Email is required';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
              return 'Please enter a valid email address';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // Phone Number
        TextFormField(
          controller: _phoneController,
          decoration: const InputDecoration(
            labelText: 'Phone Number',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
        ),

        const SizedBox(height: 16),

        // Date of Birth
        _buildDateField(
          label: 'Date of Birth',
          selectedDate: _selectedDateOfBirth,
          onDateSelected: (date) {
            setState(() {
              _selectedDateOfBirth = date;
            });
          },
          icon: Icons.cake,
        ),

        const SizedBox(height: 16),

        // Gender
        DropdownButtonFormField<String>(
          value: _selectedGender,
          decoration: const InputDecoration(
            labelText: 'Gender',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person_outline),
          ),
          items: const [
            DropdownMenuItem(value: 'Male', child: Text('Male')),
            DropdownMenuItem(value: 'Female', child: Text('Female')),
            DropdownMenuItem(value: 'Other', child: Text('Other')),
            DropdownMenuItem(value: 'Prefer not to say', child: Text('Prefer not to say')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedGender = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // Address
        TextFormField(
          controller: _addressController,
          decoration: const InputDecoration(
            labelText: 'Address',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.location_on),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  /// Build academic information section
  Widget _buildAcademicInfoSection() {
    return _buildFormSection(
      title: 'Academic Information',
      icon: Icons.school,
      children: [
        // Status
        DropdownButtonFormField<StudentStatus>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            labelText: 'Status *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.info),
          ),
          items: StudentStatus.values.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(status.displayName),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedStatus = value;
              });
            }
          },
        ),

        const SizedBox(height: 16),

        // Grade Level
        TextFormField(
          controller: _gradeLevelController,
          decoration: const InputDecoration(
            labelText: 'Grade Level',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.grade),
          ),
        ),

        const SizedBox(height: 16),

        // Major/Program
        TextFormField(
          controller: _majorController,
          decoration: const InputDecoration(
            labelText: 'Major/Program',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.book),
          ),
        ),

        const SizedBox(height: 16),

        // GPA
        TextFormField(
          controller: _gpaController,
          decoration: const InputDecoration(
            labelText: 'GPA',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.star),
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value?.isNotEmpty == true) {
              final gpa = double.tryParse(value!);
              if (gpa == null || gpa < 0 || gpa > 4.0) {
                return 'Please enter a valid GPA (0.0 - 4.0)';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build guardian information section
  Widget _buildGuardianInfoSection() {
    return _buildFormSection(
      title: 'Guardian Information',
      icon: Icons.family_restroom,
      children: [
        // Guardian Name
        TextFormField(
          controller: _guardianNameController,
          decoration: const InputDecoration(
            labelText: 'Guardian Name',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
        ),

        const SizedBox(height: 16),

        // Guardian Relationship
        TextFormField(
          controller: _guardianRelationshipController,
          decoration: const InputDecoration(
            labelText: 'Relationship',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.family_restroom),
          ),
        ),

        const SizedBox(height: 16),

        // Guardian Phone
        TextFormField(
          controller: _guardianPhoneController,
          decoration: const InputDecoration(
            labelText: 'Guardian Phone',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
        ),

        const SizedBox(height: 16),

        // Guardian Email
        TextFormField(
          controller: _guardianEmailController,
          decoration: const InputDecoration(
            labelText: 'Guardian Email',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.email),
          ),
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  /// Build emergency contact section
  Widget _buildEmergencyContactSection() {
    return _buildFormSection(
      title: 'Emergency Contact',
      icon: Icons.emergency,
      children: [
        // Emergency Contact Name
        TextFormField(
          controller: _emergencyNameController,
          decoration: const InputDecoration(
            labelText: 'Emergency Contact Name',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
        ),

        const SizedBox(height: 16),

        // Emergency Contact Relationship
        TextFormField(
          controller: _emergencyRelationshipController,
          decoration: const InputDecoration(
            labelText: 'Relationship',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.family_restroom),
          ),
        ),

        const SizedBox(height: 16),

        // Emergency Contact Phone
        TextFormField(
          controller: _emergencyPhoneController,
          decoration: const InputDecoration(
            labelText: 'Emergency Contact Phone',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
        ),

        const SizedBox(height: 16),

        // Emergency Contact Email
        TextFormField(
          controller: _emergencyEmailController,
          decoration: const InputDecoration(
            labelText: 'Emergency Contact Email',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.email),
          ),
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  /// Build form section wrapper
  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue.shade600, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  /// Build date field
  Widget _buildDateField({
    required String label,
    required DateTime? selectedDate,
    required Function(DateTime) onDateSelected,
    required IconData icon,
  }) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: selectedDate ?? DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime(2100),
        );
        if (date != null) {
          onDateSelected(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          prefixIcon: Icon(icon),
        ),
        child: Text(
          selectedDate != null
              ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
              : 'Select date',
          style: TextStyle(
            color: selectedDate != null ? Colors.black87 : Colors.grey,
          ),
        ),
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isLoading
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 12),
                      Text('Updating...'),
                    ],
                  )
                : const Text(
                    'Update Student',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
          ),
        ),
      ],
    );
  }

  /// Submit form and update student
  void _submitForm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Create updated student object
    final updatedStudent = widget.student.copyWith(
      fullName: _fullNameController.text.trim(),
      email: _emailController.text.trim(),
      phoneNumber: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
      dateOfBirth: _selectedDateOfBirth,
      gender: _selectedGender,
      address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
      status: _selectedStatus,
      gradeLevel: _gradeLevelController.text.trim().isEmpty ? null : _gradeLevelController.text.trim(),
      major: _majorController.text.trim().isEmpty ? null : _majorController.text.trim(),
      gpa: _gpaController.text.trim().isEmpty ? null : double.tryParse(_gpaController.text.trim()),
      totalCredits: _creditsController.text.trim().isEmpty ? 0 : int.tryParse(_creditsController.text.trim()) ?? 0,
      guardianInfo: _buildGuardianInfo(),
      emergencyContact: _buildEmergencyContact(),
    );

    // Dispatch update event
    _studentBloc.add(UpdateStudentEvent(updatedStudent));
  }

  /// Build guardian info object
  GuardianInfo? _buildGuardianInfo() {
    if (_guardianNameController.text.trim().isEmpty) {
      return null;
    }

    return GuardianInfo(
      name: _guardianNameController.text.trim(),
      relationship: _guardianRelationshipController.text.trim(),
      phoneNumber: _guardianPhoneController.text.trim(),
      email: _guardianEmailController.text.trim().isEmpty ? null : _guardianEmailController.text.trim(),
    );
  }

  /// Build emergency contact object
  EmergencyContact? _buildEmergencyContact() {
    if (_emergencyNameController.text.trim().isEmpty) {
      return null;
    }

    return EmergencyContact(
      name: _emergencyNameController.text.trim(),
      relationship: _emergencyRelationshipController.text.trim(),
      phoneNumber: _emergencyPhoneController.text.trim(),
      email: _emergencyEmailController.text.trim().isEmpty ? null : _emergencyEmailController.text.trim(),
    );
  }
}
