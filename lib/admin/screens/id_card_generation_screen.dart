import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import '../blocs/student/student_bloc.dart';
import '../blocs/student/student_event.dart';
import '../blocs/student/student_state.dart';
import '../models/student_model.dart';
import '../models/id_card_model.dart';
import '../services/id_card_templates_service.dart';
import '../utils/responsive_utils.dart';

/// ID Card Generation Screen for creating student ID cards
/// 
/// This screen provides:
/// - Template selection with preview
/// - Export format options (PDF, CDR, PNG, SVG)
/// - Real-time preview of ID card design
/// - Batch generation capabilities
/// - Beautiful responsive design with animations
class IDCardGenerationScreen extends StatefulWidget {
  final Student student;
  final List<Student>? students; // For batch generation

  const IDCardGenerationScreen({
    super.key,
    required this.student,
    this.students,
  });

  @override
  State<IDCardGenerationScreen> createState() => _IDCardGenerationScreenState();
}

class _IDCardGenerationScreenState extends State<IDCardGenerationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  IDCardTemplate? _selectedTemplate;
  ExportFormat _selectedFormat = ExportFormat.pdf;
  bool _isGenerating = false;
  bool _isBatchMode = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDefaultTemplate();
    _isBatchMode = widget.students != null && widget.students!.isNotEmpty;
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadDefaultTemplate() {
    final templates = IDCardTemplatesService.getAllTemplates();
    if (templates.isNotEmpty) {
      setState(() {
        _selectedTemplate = templates.first;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: BlocListener<StudentBloc, StudentState>(
        listener: _handleBlocState,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildBody(),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _isBatchMode 
            ? 'Generate ID Cards (${widget.students!.length} students)'
            : 'Generate ID Card - ${widget.student.fullName}',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.indigo.shade600,
      foregroundColor: Colors.white,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.indigo.shade600, Colors.indigo.shade800],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
    );
  }

  /// Handle BLoC state changes
  void _handleBlocState(BuildContext context, StudentState state) {
    if (state is IDCardGenerating || state is BatchIDCardGenerating) {
      setState(() {
        _isGenerating = true;
      });
    } else if (state is IDCardGenerated) {
      setState(() {
        _isGenerating = false;
      });
      _showSuccessDialog(state.idCardFile.path);
    } else if (state is BatchIDCardGenerated) {
      setState(() {
        _isGenerating = false;
      });
      _showBatchSuccessDialog(state.successCount, state.failureCount);
    } else if (state is StudentError) {
      setState(() {
        _isGenerating = false;
      });
      _showErrorDialog(state.message);
    }
  }

  /// Build main body
  Widget _buildBody() {
    return ResponsiveUtils.isMobile(context)
        ? _buildMobileLayout()
        : _buildDesktopLayout();
  }

  /// Build mobile layout
  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTemplateSelection(),
          const SizedBox(height: 24),
          _buildFormatSelection(),
          const SizedBox(height: 24),
          _buildPreviewSection(),
          const SizedBox(height: 32),
          _buildGenerateButton(),
        ],
      ),
    );
  }

  /// Build desktop layout
  Widget _buildDesktopLayout() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left panel - Template and format selection
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTemplateSelection(),
                const SizedBox(height: 24),
                _buildFormatSelection(),
                const SizedBox(height: 32),
                _buildGenerateButton(),
              ],
            ),
          ),
          const SizedBox(width: 32),
          // Right panel - Preview
          Expanded(
            flex: 1,
            child: _buildPreviewSection(),
          ),
        ],
      ),
    );
  }

  /// Build template selection section
  Widget _buildTemplateSelection() {
    final templates = IDCardTemplatesService.getAllTemplates();
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.palette, color: Colors.indigo.shade600),
                const SizedBox(width: 8),
                Text(
                  'Select Template',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: ResponsiveUtils.isMobile(context) ? 2 : 3,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.5,
              ),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                final isSelected = _selectedTemplate?.id == template.id;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTemplate = template;
                    });
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Colors.indigo.shade600 : Colors.grey.shade300,
                        width: isSelected ? 3 : 1,
                      ),
                      gradient: LinearGradient(
                        colors: [
                          Color(int.parse(template.primaryColor.replaceFirst('#', '0xFF'))),
                          Color(int.parse(template.secondaryColor.replaceFirst('#', '0xFF'))),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.badge,
                                color: Colors.white,
                                size: 24,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                template.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.check,
                                color: Colors.indigo.shade600,
                                size: 16,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build format selection section
  Widget _buildFormatSelection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.file_download, color: Colors.indigo.shade600),
                const SizedBox(width: 8),
                Text(
                  'Export Format',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...ExportFormat.values.map((format) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: RadioListTile<ExportFormat>(
                  title: Text(format.displayName),
                  subtitle: Text(_getFormatDescription(format)),
                  value: format,
                  groupValue: _selectedFormat,
                  onChanged: (value) {
                    setState(() {
                      _selectedFormat = value!;
                    });
                  },
                  activeColor: Colors.indigo.shade600,
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// Get format description
  String _getFormatDescription(ExportFormat format) {
    switch (format) {
      case ExportFormat.pdf:
        return 'Best for printing and sharing';
      case ExportFormat.cdr:
        return 'CorelDRAW format for professional editing';
      case ExportFormat.png:
        return 'High-quality image format';
      case ExportFormat.svg:
        return 'Scalable vector graphics';
    }
  }

  /// Build preview section
  Widget _buildPreviewSection() {
    if (_selectedTemplate == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(40),
          child: Center(
            child: Text('Select a template to see preview'),
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.preview, color: Colors.indigo.shade600),
                const SizedBox(width: 8),
                Text(
                  'Preview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: _buildIDCardPreview(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build ID card preview
  Widget _buildIDCardPreview() {
    final template = _selectedTemplate!;
    final student = widget.student;

    return Container(
      width: 300,
      height: 190,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            Color(int.parse(template.primaryColor.replaceFirst('#', '0xFF'))),
            Color(int.parse(template.secondaryColor.replaceFirst('#', '0xFF'))),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Institution logo placeholder
          Positioned(
            left: 15,
            top: 15,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.school, color: Colors.grey),
            ),
          ),

          // Student photo placeholder
          Positioned(
            left: 20,
            top: 70,
            child: Container(
              width: 60,
              height: 75,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: student.profilePhotoUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.network(
                        student.profilePhotoUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.person, color: Colors.grey);
                        },
                      ),
                    )
                  : const Icon(Icons.person, color: Colors.grey),
            ),
          ),

          // QR code placeholder
          Positioned(
            right: 20,
            top: 70,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Icon(Icons.qr_code, color: Colors.grey),
            ),
          ),

          // Student information
          Positioned(
            left: 20,
            bottom: 40,
            right: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  student.fullName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  'ID: ${student.studentId}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  student.major ?? 'General Studies',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Institution name
          Positioned(
            left: 15,
            bottom: 8,
            child: Text(
              'Passion Learning Institute',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 8,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build generate button
  Widget _buildGenerateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isGenerating ? null : _generateIDCard,
        icon: _isGenerating
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.download),
        label: Text(
          _isGenerating
              ? 'Generating...'
              : _isBatchMode
                  ? 'Generate All ID Cards'
                  : 'Generate ID Card',
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.indigo.shade600,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  /// Generate ID card
  void _generateIDCard() {
    if (_selectedTemplate == null) {
      _showErrorDialog('Please select a template first');
      return;
    }

    final bloc = context.read<StudentBloc>();

    if (_isBatchMode) {
      // Batch generation
      final studentIds = widget.students!.map((s) => s.id).toList();
      bloc.add(BatchGenerateIDCardsEvent(
        studentIds: studentIds,
        template: _selectedTemplate!,
        exportFormat: _selectedFormat,
      ));
    } else {
      // Single generation
      bloc.add(GenerateIDCardEvent(
        studentId: widget.student.id,
        template: _selectedTemplate!,
        exportFormat: _selectedFormat,
      ));
    }
  }

  /// Show success dialog
  void _showSuccessDialog(String filePath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Success!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 48),
            const SizedBox(height: 16),
            Text('ID card generated successfully!\n\nSaved to: $filePath'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(true);
            },
            child: const Text('OK'),
          ),
          ElevatedButton(
            onPressed: () {
              Share.shareXFiles([XFile(filePath)]);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  /// Show batch success dialog
  void _showBatchSuccessDialog(int successCount, int failureCount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Batch Generation Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 48),
            const SizedBox(height: 16),
            Text(
              'Generated $successCount ID cards successfully!'
              '${failureCount > 0 ? '\n$failureCount failed.' : ''}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(true);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
