import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:image/image.dart' as img;
import 'package:http/http.dart' as http;
import '../models/id_card_model.dart';
import '../models/student_model.dart';

/// Service for generating student ID cards in various formats
/// 
/// This service provides:
/// - PDF generation for printing
/// - CDR-compatible vector output
/// - QR code generation and integration
/// - Multiple template support
/// - Batch generation capabilities
class IDCardGenerationService {
  /// Generate ID card in PDF format
  static Future<File> generatePDF({
    required IDCard idCard,
    String? customPath,
  }) async {
    try {
      final pdf = pw.Document();
      
      // Generate QR code image
      final qrCodeImage = await _generateQRCodeImage(idCard.qrCodeData);
      
      // Load institution logo if available
      pw.ImageProvider? logoImage;
      if (idCard.institutionInfo.logoUrl != null) {
        try {
          logoImage = await _loadImageFromAsset(idCard.institutionInfo.logoUrl!);
        } catch (e) {
          print('Warning: Could not load logo: $e');
        }
      }
      
      // Load student photo if available, with fallback to user icon
      pw.ImageProvider? studentPhoto;
      if (idCard.student.profilePhotoUrl != null && idCard.student.profilePhotoUrl!.isNotEmpty) {
        try {
          studentPhoto = await _loadImageFromUrl(idCard.student.profilePhotoUrl!);
        } catch (e) {
          print('Warning: Could not load student photo, using fallback icon: $e');
          // Use user icon fallback
          studentPhoto = await _createUserIconImage();
        }
      } else {
        // No photo URL provided, use user icon
        studentPhoto = await _createUserIconImage();
      }

      // Add page to PDF
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat(
            idCard.template.dimensions.width * PdfPageFormat.mm,
            idCard.template.dimensions.height * PdfPageFormat.mm,
          ),
          build: (pw.Context context) {
            return _buildPDFCard(
              idCard: idCard,
              qrCodeImage: qrCodeImage,
              logoImage: logoImage,
              studentPhoto: studentPhoto,
            );
          },
        ),
      );

      // Save PDF file
      final directory = customPath != null 
          ? Directory(customPath)
          : await getApplicationDocumentsDirectory();
      
      final fileName = 'ID_Card_${idCard.student.studentId}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      
      await file.writeAsBytes(await pdf.save());
      
      return file;
    } catch (e) {
      throw Exception('Failed to generate PDF: $e');
    }
  }

  /// Generate ID card in CDR-compatible format (SVG)
  static Future<File> generateCDR({
    required IDCard idCard,
    String? customPath,
  }) async {
    try {
      // Generate SVG content for CDR compatibility
      final svgContent = _generateSVGContent(idCard);
      
      // Save SVG file
      final directory = customPath != null 
          ? Directory(customPath)
          : await getApplicationDocumentsDirectory();
      
      final fileName = 'ID_Card_${idCard.student.studentId}_${DateTime.now().millisecondsSinceEpoch}.svg';
      final file = File('${directory.path}/$fileName');
      
      await file.writeAsString(svgContent);
      
      return file;
    } catch (e) {
      throw Exception('Failed to generate CDR file: $e');
    }
  }

  /// Generate ID card preview as PNG image
  static Future<File> generatePreviewImage({
    required IDCard idCard,
    String? customPath,
    double scale = 2.0,
  }) async {
    try {
      // Create a widget representation of the ID card
      final cardWidget = _buildCardWidget(idCard);
      
      // Convert widget to image
      final imageBytes = await _widgetToImage(cardWidget, scale);
      
      // Save image file
      final directory = customPath != null 
          ? Directory(customPath)
          : await getApplicationDocumentsDirectory();
      
      final fileName = 'ID_Card_Preview_${idCard.student.studentId}_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File('${directory.path}/$fileName');
      
      await file.writeAsBytes(imageBytes);
      
      return file;
    } catch (e) {
      throw Exception('Failed to generate preview image: $e');
    }
  }

  /// Generate QR code image
  static Future<pw.ImageProvider> _generateQRCodeImage(String data) async {
    try {
      final qrValidationResult = QrValidator.validate(
        data: data,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
      );

      if (qrValidationResult.status == QrValidationStatus.valid) {
        final qrCode = qrValidationResult.qrCode!;
        final painter = QrPainter.withQr(
          qr: qrCode,
          color: const Color(0xFF000000),
          emptyColor: const Color(0xFFFFFFFF),
          gapless: false,
        );

        final picData = await painter.toImageData(200);
        final bytes = picData!.buffer.asUint8List();
        
        return pw.MemoryImage(bytes);
      } else {
        throw Exception('Invalid QR code data');
      }
    } catch (e) {
      throw Exception('Failed to generate QR code: $e');
    }
  }

  /// Load image from asset
  static Future<pw.ImageProvider> _loadImageFromAsset(String assetPath) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();
      return pw.MemoryImage(bytes);
    } catch (e) {
      throw Exception('Failed to load asset image: $e');
    }
  }

  /// Load image from URL with proper HTTP client
  static Future<pw.ImageProvider> _loadImageFromUrl(String url) async {
    try {
      // Try to load image from URL
      final response = await http.get(Uri.parse(url)).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Image load timeout'),
      );

      if (response.statusCode == 200) {
        return pw.MemoryImage(response.bodyBytes);
      } else {
        throw Exception('Failed to load image: HTTP ${response.statusCode}');
      }
    } catch (e) {
      print('Error loading image from URL: $e');
      // Return user icon fallback instead of throwing
      return await _createUserIconImage();
    }
  }

  /// Create a user icon image for missing photos
  static Future<pw.ImageProvider> _createUserIconImage() async {
    try {
      // Create a professional user icon image
      final image = img.Image(width: 150, height: 150);

      // Fill with light background
      img.fill(image, color: img.ColorRgb8(240, 240, 240));

      // Draw circular background for icon
      final centerX = 75;
      final centerY = 75;
      final radius = 60;

      // Draw circle background
      img.fillCircle(image,
        x: centerX,
        y: centerY,
        radius: radius,
        color: img.ColorRgb8(100, 150, 200)
      );

      // Draw simple user icon shape (head and shoulders)
      // Head circle
      img.fillCircle(image,
        x: centerX,
        y: centerY - 15,
        radius: 20,
        color: img.ColorRgb8(255, 255, 255)
      );

      // Shoulders/body
      img.fillRect(image,
        x1: centerX - 25, y1: centerY + 10,
        x2: centerX + 25, y2: centerY + 40,
        color: img.ColorRgb8(255, 255, 255)
      );

      // Add border
      img.drawCircle(image,
        x: centerX,
        y: centerY,
        radius: radius,
        color: img.ColorRgb8(80, 120, 160)
      );

      final bytes = Uint8List.fromList(img.encodePng(image));
      return pw.MemoryImage(bytes);
    } catch (e) {
      print('Error creating user icon: $e');
      // Fallback to simple placeholder
      return await _createSimplePlaceholder();
    }
  }

  /// Create a simple placeholder image as last resort
  static Future<pw.ImageProvider> _createSimplePlaceholder() async {
    final image = img.Image(width: 150, height: 150);
    img.fill(image, color: img.ColorRgb8(200, 200, 200));

    // Add border
    img.drawRect(image,
      x1: 0, y1: 0, x2: 149, y2: 149,
      color: img.ColorRgb8(150, 150, 150));

    // Add "USER" text area
    img.fillRect(image,
      x1: 30, y1: 60, x2: 120, y2: 90,
      color: img.ColorRgb8(150, 150, 150));

    final bytes = Uint8List.fromList(img.encodePng(image));
    return pw.MemoryImage(bytes);
  }

  /// Build PDF card layout
  static pw.Widget _buildPDFCard({
    required IDCard idCard,
    required pw.ImageProvider qrCodeImage,
    pw.ImageProvider? logoImage,
    pw.ImageProvider? studentPhoto,
  }) {
    final template = idCard.template;
    final student = idCard.student;
    
    return pw.Container(
      width: template.dimensions.width * PdfPageFormat.mm,
      height: template.dimensions.height * PdfPageFormat.mm,
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex(template.primaryColor),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Stack(
        children: [
          // Background gradient effect
          pw.Container(
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [
                  PdfColor.fromHex(template.primaryColor),
                  PdfColor.fromHex(template.secondaryColor),
                ],
                begin: pw.Alignment.topLeft,
                end: pw.Alignment.bottomRight,
              ),
              borderRadius: pw.BorderRadius.circular(8),
            ),
          ),
          
          // Institution logo
          if (logoImage != null)
            pw.Positioned(
              left: template.logoSpec.x,
              top: template.logoSpec.y,
              child: pw.Container(
                width: template.logoSpec.width,
                height: template.logoSpec.height,
                child: pw.Image(logoImage),
              ),
            ),
          
          // Student photo (always present - either real photo or user icon)
          pw.Positioned(
            left: template.photoSpec.x,
            top: template.photoSpec.y,
            child: pw.Container(
              width: template.photoSpec.width,
              height: template.photoSpec.height,
              decoration: template.photoSpec.isCircular
                  ? pw.BoxDecoration(
                      shape: pw.BoxShape.circle,
                      image: pw.DecorationImage(
                        image: studentPhoto!,
                        fit: pw.BoxFit.cover,
                      ),
                      border: pw.Border.all(
                        color: PdfColors.grey300,
                        width: 1,
                      ),
                    )
                  : pw.BoxDecoration(
                      border: pw.Border.all(
                        color: PdfColors.grey300,
                        width: 1,
                      ),
                    ),
              child: template.photoSpec.isCircular
                  ? null
                  : pw.ClipRRect(
                      borderRadius: pw.BorderRadius.circular(4),
                      child: pw.Image(studentPhoto!, fit: pw.BoxFit.cover),
                    ),
            ),
          ),
          
          // QR Code
          pw.Positioned(
            left: template.qrSpec.x,
            top: template.qrSpec.y,
            child: pw.Container(
              width: template.qrSpec.size,
              height: template.qrSpec.size,
              child: pw.Image(qrCodeImage),
            ),
          ),
          
          // Student name
          pw.Positioned(
            left: template.textLayout.nameX,
            top: template.textLayout.nameY,
            child: pw.Text(
              student.fullName,
              style: pw.TextStyle(
                fontSize: template.textLayout.nameFontSize,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.white,
              ),
            ),
          ),
          
          // Student ID
          pw.Positioned(
            left: template.textLayout.idX,
            top: template.textLayout.idY,
            child: pw.Text(
              'ID: ${student.studentId}',
              style: pw.TextStyle(
                fontSize: template.textLayout.idFontSize,
                color: PdfColors.white,
              ),
            ),
          ),
          
          // Program/Major
          pw.Positioned(
            left: template.textLayout.programX,
            top: template.textLayout.programY,
            child: pw.Text(
              student.major ?? 'General Studies',
              style: pw.TextStyle(
                fontSize: template.textLayout.programFontSize,
                color: PdfColors.white,
              ),
            ),
          ),
          
          // Institution name
          pw.Positioned(
            left: 10,
            bottom: 5,
            child: pw.Text(
              idCard.institutionInfo.name,
              style: pw.TextStyle(
                fontSize: 6,
                color: PdfColors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Generate SVG content for CDR compatibility
  static String _generateSVGContent(IDCard idCard) {
    final template = idCard.template;
    final student = idCard.student;
    
    return '''
<?xml version="1.0" encoding="UTF-8"?>
<svg width="${template.dimensions.width}mm" height="${template.dimensions.height}mm" 
     viewBox="0 0 ${template.dimensions.width} ${template.dimensions.height}" 
     xmlns="http://www.w3.org/2000/svg">
  
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${template.primaryColor};stop-opacity:1" />
      <stop offset="100%" style="stop-color:${template.secondaryColor};stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#bgGradient)" rx="8" ry="8"/>
  
  <!-- Student Name -->
  <text x="${template.textLayout.nameX}" y="${template.textLayout.nameY}" 
        font-family="Arial, sans-serif" font-size="${template.textLayout.nameFontSize}" 
        font-weight="bold" fill="white">${student.fullName}</text>
  
  <!-- Student ID -->
  <text x="${template.textLayout.idX}" y="${template.textLayout.idY}" 
        font-family="Arial, sans-serif" font-size="${template.textLayout.idFontSize}" 
        fill="white">ID: ${student.studentId}</text>
  
  <!-- Program -->
  <text x="${template.textLayout.programX}" y="${template.textLayout.programY}" 
        font-family="Arial, sans-serif" font-size="${template.textLayout.programFontSize}" 
        fill="white">${student.major ?? 'General Studies'}</text>
  
  <!-- Institution Name -->
  <text x="10" y="${template.dimensions.height - 5}" 
        font-family="Arial, sans-serif" font-size="6" 
        fill="white">${idCard.institutionInfo.name}</text>
  
  <!-- QR Code placeholder -->
  <rect x="${template.qrSpec.x}" y="${template.qrSpec.y}" 
        width="${template.qrSpec.size}" height="${template.qrSpec.size}" 
        fill="white" stroke="black" stroke-width="1"/>
  <text x="${template.qrSpec.x + template.qrSpec.size / 2}" 
        y="${template.qrSpec.y + template.qrSpec.size / 2}" 
        text-anchor="middle" dominant-baseline="middle" 
        font-family="Arial, sans-serif" font-size="4" fill="black">QR CODE</text>
  
  <!-- Photo placeholder -->
  <rect x="${template.photoSpec.x}" y="${template.photoSpec.y}" 
        width="${template.photoSpec.width}" height="${template.photoSpec.height}" 
        fill="white" stroke="black" stroke-width="1"/>
  <text x="${template.photoSpec.x + template.photoSpec.width / 2}" 
        y="${template.photoSpec.y + template.photoSpec.height / 2}" 
        text-anchor="middle" dominant-baseline="middle" 
        font-family="Arial, sans-serif" font-size="6" fill="black">PHOTO</text>
  
</svg>''';
  }





  /// Convert widget to image bytes
  static Future<Uint8List> _widgetToImage(Widget widget, double scale) async {
    try {
      // Create a simple placeholder image for now
      // In production, you would use flutter's rendering system
      final image = img.Image(width: 400, height: 250);
      img.fill(image, color: img.ColorRgb8(33, 150, 243));

      // Add some basic text
      img.drawRect(image,
        x1: 20, y1: 100, x2: 380, y2: 130,
        color: img.ColorRgb8(255, 255, 255));

      return Uint8List.fromList(img.encodePng(image));
    } catch (e) {
      throw Exception('Failed to convert widget to image: $e');
    }
  }

  /// Build card widget for preview
  static Widget _buildCardWidget(IDCard idCard) {
    final template = idCard.template;
    final student = idCard.student;

    return Container(
      width: template.dimensions.width * 3.78, // Convert mm to pixels (approximate)
      height: template.dimensions.height * 3.78,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(int.parse(template.primaryColor.replaceFirst('#', '0xFF'))),
            Color(int.parse(template.secondaryColor.replaceFirst('#', '0xFF'))),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // Institution Name
          Positioned(
            top: 20,
            left: 0,
            right: 0,
            child: Text(
              idCard.institutionInfo.name,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Student Name
          Positioned(
            left: template.textLayout.nameX * 3.78,
            top: template.textLayout.nameY * 3.78,
            child: Text(
              student.fullName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Student ID
          Positioned(
            left: template.textLayout.idX * 3.78,
            top: template.textLayout.idY * 3.78,
            child: Text(
              'ID: ${student.studentId}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),

          // Major/Program
          Positioned(
            left: template.textLayout.programX * 3.78,
            top: template.textLayout.programY * 3.78,
            child: Text(
              'Major: ${student.major ?? 'General Studies'}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),

          // Photo placeholder
          Positioned(
            left: template.photoSpec.x * 3.78,
            top: template.photoSpec.y * 3.78,
            child: Container(
              width: template.photoSpec.width * 3.78,
              height: template.photoSpec.height * 3.78,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                border: Border.all(color: Colors.black),
                shape: template.photoSpec.isCircular ? BoxShape.circle : BoxShape.rectangle,
              ),
              child: const Center(
                child: Text(
                  'PHOTO',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ),

          // QR Code placeholder
          Positioned(
            left: template.qrSpec.x * 3.78,
            top: template.qrSpec.y * 3.78,
            child: Container(
              width: template.qrSpec.size * 3.78,
              height: template.qrSpec.size * 3.78,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.black),
              ),
              child: const Center(
                child: Text(
                  'QR',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Batch generate ID cards for multiple students
  static Future<List<File>> batchGenerate({
    required List<Student> students,
    required IDCardTemplate template,
    required InstitutionInfo institutionInfo,
    required ExportFormat format,
    String? customPath,
  }) async {
    final files = <File>[];
    
    for (final student in students) {
      try {
        final idCard = IDCard.fromStudent(
          student: student,
          template: template,
          institutionInfo: institutionInfo,
        );
        
        File file;
        switch (format) {
          case ExportFormat.pdf:
            file = await generatePDF(idCard: idCard, customPath: customPath);
            break;
          case ExportFormat.cdr:
            file = await generateCDR(idCard: idCard, customPath: customPath);
            break;
          case ExportFormat.png:
            file = await generatePreviewImage(idCard: idCard, customPath: customPath);
            break;
          case ExportFormat.svg:
            file = await generateCDR(idCard: idCard, customPath: customPath);
            break;
        }
        
        files.add(file);
      } catch (e) {
        print('Failed to generate ID card for ${student.fullName}: $e');
      }
    }
    
    return files;
  }
}
