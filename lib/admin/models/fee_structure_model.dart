import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Fee structure model for managing institutional fee structures
/// 
/// This model handles:
/// - Different fee types (tuition, library, lab, etc.)
/// - Academic year and term-based fee structures
/// - Grade/class level specific fees
/// - Discount and scholarship management
/// - Installment payment plans
class FeeStructure extends Equatable {
  /// Unique identifier for the fee structure
  final String id;
  
  /// Fee structure name/title
  final String name;
  
  /// Academic year this fee structure applies to
  final String academicYear;
  
  /// Academic term (semester/quarter)
  final String? academicTerm;
  
  /// Grade/class level this applies to
  final String gradeLevel;
  
  /// Base tuition fee amount
  final double tuitionFee;
  
  /// Additional fees breakdown
  final Map<String, double> additionalFees;
  
  /// Total fee amount (calculated)
  final double totalFee;
  
  /// Whether installment payments are allowed
  final bool allowInstallments;
  
  /// Number of installments allowed
  final int? installmentCount;
  
  /// Installment amount (if applicable)
  final double? installmentAmount;
  
  /// Late fee penalty amount
  final double lateFee;
  
  /// Late fee grace period in days
  final int lateFeeDays;
  
  /// Available discounts
  final List<FeeDiscount> discounts;
  
  /// Fee due date
  final DateTime dueDate;
  
  /// Whether this fee structure is active
  final bool isActive;
  
  /// Creation timestamp
  final DateTime createdAt;
  
  /// Last update timestamp
  final DateTime? updatedAt;
  
  /// Created by (admin user ID)
  final String createdBy;
  
  /// Additional notes
  final String? notes;

  const FeeStructure({
    required this.id,
    required this.name,
    required this.academicYear,
    this.academicTerm,
    required this.gradeLevel,
    required this.tuitionFee,
    this.additionalFees = const {},
    required this.totalFee,
    this.allowInstallments = false,
    this.installmentCount,
    this.installmentAmount,
    this.lateFee = 0.0,
    this.lateFeeDays = 30,
    this.discounts = const [],
    required this.dueDate,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.notes,
  });

  /// Factory constructor to create FeeStructure from Firebase JSON data
  factory FeeStructure.fromJson(Map<String, dynamic> json, String id) {
    try {
      final additionalFeesMap = Map<String, double>.from(
        (json['additionalFees'] as Map<String, dynamic>? ?? {})
            .map((key, value) => MapEntry(key, _parseDouble(value))),
      );
      
      final discountsList = (json['discounts'] as List<dynamic>? ?? [])
          .map((discount) => FeeDiscount.fromJson(discount as Map<String, dynamic>))
          .toList();

      return FeeStructure(
        id: id,
        name: json['name']?.toString() ?? '',
        academicYear: json['academicYear']?.toString() ?? '',
        academicTerm: json['academicTerm']?.toString(),
        gradeLevel: json['gradeLevel']?.toString() ?? '',
        tuitionFee: _parseDouble(json['tuitionFee']),
        additionalFees: additionalFeesMap,
        totalFee: _parseDouble(json['totalFee']),
        allowInstallments: json['allowInstallments'] ?? false,
        installmentCount: json['installmentCount'],
        installmentAmount: json['installmentAmount'] != null 
            ? _parseDouble(json['installmentAmount']) 
            : null,
        lateFee: _parseDouble(json['lateFee'], defaultValue: 0.0),
        lateFeeDays: json['lateFeeDays'] ?? 30,
        discounts: discountsList,
        dueDate: _parseTimestamp(json['dueDate']),
        isActive: json['isActive'] ?? true,
        createdAt: _parseTimestamp(json['createdAt']),
        updatedAt: json['updatedAt'] != null ? _parseTimestamp(json['updatedAt']) : null,
        createdBy: json['createdBy']?.toString() ?? '',
        notes: json['notes']?.toString(),
      );
    } catch (e) {
      throw Exception('Failed to parse FeeStructure from JSON: $e');
    }
  }

  /// Convert FeeStructure instance to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'academicYear': academicYear,
      'academicTerm': academicTerm,
      'gradeLevel': gradeLevel,
      'tuitionFee': tuitionFee,
      'additionalFees': additionalFees,
      'totalFee': totalFee,
      'allowInstallments': allowInstallments,
      'installmentCount': installmentCount,
      'installmentAmount': installmentAmount,
      'lateFee': lateFee,
      'lateFeeDays': lateFeeDays,
      'discounts': discounts.map((discount) => discount.toJson()).toList(),
      'dueDate': Timestamp.fromDate(dueDate),
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'notes': notes,
    };
  }

  /// Create a copy of the fee structure with updated fields
  FeeStructure copyWith({
    String? id,
    String? name,
    String? academicYear,
    String? academicTerm,
    String? gradeLevel,
    double? tuitionFee,
    Map<String, double>? additionalFees,
    double? totalFee,
    bool? allowInstallments,
    int? installmentCount,
    double? installmentAmount,
    double? lateFee,
    int? lateFeeDays,
    List<FeeDiscount>? discounts,
    DateTime? dueDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? notes,
  }) {
    return FeeStructure(
      id: id ?? this.id,
      name: name ?? this.name,
      academicYear: academicYear ?? this.academicYear,
      academicTerm: academicTerm ?? this.academicTerm,
      gradeLevel: gradeLevel ?? this.gradeLevel,
      tuitionFee: tuitionFee ?? this.tuitionFee,
      additionalFees: additionalFees ?? this.additionalFees,
      totalFee: totalFee ?? this.totalFee,
      allowInstallments: allowInstallments ?? this.allowInstallments,
      installmentCount: installmentCount ?? this.installmentCount,
      installmentAmount: installmentAmount ?? this.installmentAmount,
      lateFee: lateFee ?? this.lateFee,
      lateFeeDays: lateFeeDays ?? this.lateFeeDays,
      discounts: discounts ?? this.discounts,
      dueDate: dueDate ?? this.dueDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      createdBy: createdBy ?? this.createdBy,
      notes: notes ?? this.notes,
    );
  }

  /// Calculate total fee including additional fees
  double calculateTotalFee() {
    double total = tuitionFee;
    for (double fee in additionalFees.values) {
      total += fee;
    }
    return total;
  }

  /// Calculate discounted amount for a student
  double calculateDiscountedFee(List<String> applicableDiscountIds) {
    double total = calculateTotalFee();
    double totalDiscount = 0.0;
    
    for (FeeDiscount discount in discounts) {
      if (applicableDiscountIds.contains(discount.id)) {
        if (discount.type == DiscountType.percentage) {
          totalDiscount += total * (discount.amount / 100);
        } else {
          totalDiscount += discount.amount;
        }
      }
    }
    
    return total - totalDiscount;
  }

  /// Helper method to parse double values
  static double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  /// Helper method to parse Firestore timestamps
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();
    if (timestamp is Timestamp) return timestamp.toDate();
    if (timestamp is DateTime) return timestamp;
    if (timestamp is String) return DateTime.tryParse(timestamp) ?? DateTime.now();
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        name,
        academicYear,
        academicTerm,
        gradeLevel,
        tuitionFee,
        additionalFees,
        totalFee,
        allowInstallments,
        installmentCount,
        installmentAmount,
        lateFee,
        lateFeeDays,
        discounts,
        dueDate,
        isActive,
        createdAt,
        updatedAt,
        createdBy,
        notes,
      ];

  @override
  String toString() => 'FeeStructure(id: $id, name: $name, gradeLevel: $gradeLevel, totalFee: $totalFee)';
}

/// Fee discount model
class FeeDiscount extends Equatable {
  final String id;
  final String name;
  final String description;
  final DiscountType type;
  final double amount;
  final bool isActive;
  final DateTime? validFrom;
  final DateTime? validTo;

  const FeeDiscount({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.amount,
    this.isActive = true,
    this.validFrom,
    this.validTo,
  });

  factory FeeDiscount.fromJson(Map<String, dynamic> json) {
    return FeeDiscount(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      type: DiscountType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => DiscountType.percentage,
      ),
      amount: FeeStructure._parseDouble(json['amount']),
      isActive: json['isActive'] ?? true,
      validFrom: json['validFrom'] != null ? FeeStructure._parseTimestamp(json['validFrom']) : null,
      validTo: json['validTo'] != null ? FeeStructure._parseTimestamp(json['validTo']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'amount': amount,
      'isActive': isActive,
      'validFrom': validFrom != null ? Timestamp.fromDate(validFrom!) : null,
      'validTo': validTo != null ? Timestamp.fromDate(validTo!) : null,
    };
  }

  @override
  List<Object?> get props => [id, name, description, type, amount, isActive, validFrom, validTo];
}

/// Discount type enumeration
enum DiscountType {
  percentage,
  fixed,
}
