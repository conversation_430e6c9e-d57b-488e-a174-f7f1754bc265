import 'package:equatable/equatable.dart';
import 'student_model.dart';

/// ID Card model representing a student ID card
/// 
/// This model handles:
/// - ID card template information and design specifications
/// - Student data integration for card generation
/// - QR code generation and verification links
/// - Export format specifications (PDF, CDR)
class IDCard extends Equatable {
  /// Unique identifier for the ID card
  final String id;
  
  /// Associated student information
  final Student student;
  
  /// ID card template used
  final IDCardTemplate template;
  
  /// QR code data for verification
  final String qrCodeData;
  
  /// Verification URL for online validation
  final String verificationUrl;
  
  /// Card issue date
  final DateTime issueDate;
  
  /// Card expiry date
  final DateTime expiryDate;
  
  /// Card status
  final IDCardStatus status;
  
  /// Institution/school information
  final InstitutionInfo institutionInfo;
  
  /// Additional card metadata
  final Map<String, dynamic>? metadata;

  const IDCard({
    required this.id,
    required this.student,
    required this.template,
    required this.qrCodeData,
    required this.verificationUrl,
    required this.issueDate,
    required this.expiryDate,
    this.status = IDCardStatus.active,
    required this.institutionInfo,
    this.metadata,
  });

  /// Create ID card from student data
  factory IDCard.fromStudent({
    required Student student,
    required IDCardTemplate template,
    required InstitutionInfo institutionInfo,
    DateTime? issueDate,
    DateTime? expiryDate,
  }) {
    final now = DateTime.now();
    final issue = issueDate ?? now;
    final expiry = expiryDate ?? DateTime(now.year + 4, now.month, now.day);
    
    final id = 'IDC_${student.studentId}_${now.millisecondsSinceEpoch}';
    final verificationUrl = 'https://your-domain.com/verify/${student.id}';
    final qrCodeData = 'STUDENT_ID:${student.studentId}|VERIFICATION:$verificationUrl|ISSUE:${issue.millisecondsSinceEpoch}';

    return IDCard(
      id: id,
      student: student,
      template: template,
      qrCodeData: qrCodeData,
      verificationUrl: verificationUrl,
      issueDate: issue,
      expiryDate: expiry,
      institutionInfo: institutionInfo,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentId': student.id,
      'templateId': template.id,
      'qrCodeData': qrCodeData,
      'verificationUrl': verificationUrl,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'status': status.name,
      'institutionInfo': institutionInfo.toJson(),
      'metadata': metadata,
    };
  }

  /// Create copy with updated fields
  IDCard copyWith({
    String? id,
    Student? student,
    IDCardTemplate? template,
    String? qrCodeData,
    String? verificationUrl,
    DateTime? issueDate,
    DateTime? expiryDate,
    IDCardStatus? status,
    InstitutionInfo? institutionInfo,
    Map<String, dynamic>? metadata,
  }) {
    return IDCard(
      id: id ?? this.id,
      student: student ?? this.student,
      template: template ?? this.template,
      qrCodeData: qrCodeData ?? this.qrCodeData,
      verificationUrl: verificationUrl ?? this.verificationUrl,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      institutionInfo: institutionInfo ?? this.institutionInfo,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        student,
        template,
        qrCodeData,
        verificationUrl,
        issueDate,
        expiryDate,
        status,
        institutionInfo,
        metadata,
      ];
}

/// ID Card template model
class IDCardTemplate extends Equatable {
  /// Template unique identifier
  final String id;
  
  /// Template name
  final String name;
  
  /// Template description
  final String description;
  
  /// Template design type
  final IDCardDesignType designType;
  
  /// Primary color scheme
  final String primaryColor;
  
  /// Secondary color scheme
  final String secondaryColor;
  
  /// Background pattern or image
  final String? backgroundAsset;
  
  /// Logo position and size specifications
  final LogoSpecification logoSpec;
  
  /// Photo position and size specifications
  final PhotoSpecification photoSpec;
  
  /// QR code position and size specifications
  final QRCodeSpecification qrSpec;
  
  /// Text layout specifications
  final TextLayoutSpecification textLayout;
  
  /// Card dimensions (width x height in mm)
  final CardDimensions dimensions;

  const IDCardTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.designType,
    required this.primaryColor,
    required this.secondaryColor,
    this.backgroundAsset,
    required this.logoSpec,
    required this.photoSpec,
    required this.qrSpec,
    required this.textLayout,
    required this.dimensions,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'designType': designType.name,
      'primaryColor': primaryColor,
      'secondaryColor': secondaryColor,
      'backgroundAsset': backgroundAsset,
      'logoSpec': logoSpec.toJson(),
      'photoSpec': photoSpec.toJson(),
      'qrSpec': qrSpec.toJson(),
      'textLayout': textLayout.toJson(),
      'dimensions': dimensions.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        designType,
        primaryColor,
        secondaryColor,
        backgroundAsset,
        logoSpec,
        photoSpec,
        qrSpec,
        textLayout,
        dimensions,
      ];
}

/// Institution information for ID cards
class InstitutionInfo extends Equatable {
  /// Institution name
  final String name;
  
  /// Institution logo URL or asset path
  final String? logoUrl;
  
  /// Institution address
  final String address;
  
  /// Institution website
  final String? website;
  
  /// Institution phone
  final String? phone;
  
  /// Institution email
  final String? email;

  const InstitutionInfo({
    required this.name,
    this.logoUrl,
    required this.address,
    this.website,
    this.phone,
    this.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'logoUrl': logoUrl,
      'address': address,
      'website': website,
      'phone': phone,
      'email': email,
    };
  }

  @override
  List<Object?> get props => [name, logoUrl, address, website, phone, email];
}

/// Card dimensions specification
class CardDimensions extends Equatable {
  final double width;
  final double height;
  final String unit; // 'mm', 'px', 'in'

  const CardDimensions({
    required this.width,
    required this.height,
    this.unit = 'mm',
  });

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'unit': unit,
    };
  }

  @override
  List<Object> get props => [width, height, unit];
}

/// Logo specification for positioning and sizing
class LogoSpecification extends Equatable {
  final double x;
  final double y;
  final double width;
  final double height;

  const LogoSpecification({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  Map<String, dynamic> toJson() {
    return {'x': x, 'y': y, 'width': width, 'height': height};
  }

  @override
  List<Object> get props => [x, y, width, height];
}

/// Photo specification for positioning and sizing
class PhotoSpecification extends Equatable {
  final double x;
  final double y;
  final double width;
  final double height;
  final bool isCircular;

  const PhotoSpecification({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.isCircular = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'isCircular': isCircular,
    };
  }

  @override
  List<Object> get props => [x, y, width, height, isCircular];
}

/// QR code specification for positioning and sizing
class QRCodeSpecification extends Equatable {
  final double x;
  final double y;
  final double size;

  const QRCodeSpecification({
    required this.x,
    required this.y,
    required this.size,
  });

  Map<String, dynamic> toJson() {
    return {'x': x, 'y': y, 'size': size};
  }

  @override
  List<Object> get props => [x, y, size];
}

/// Text layout specification
class TextLayoutSpecification extends Equatable {
  final double nameX;
  final double nameY;
  final double nameFontSize;
  final double idX;
  final double idY;
  final double idFontSize;
  final double programX;
  final double programY;
  final double programFontSize;

  const TextLayoutSpecification({
    required this.nameX,
    required this.nameY,
    required this.nameFontSize,
    required this.idX,
    required this.idY,
    required this.idFontSize,
    required this.programX,
    required this.programY,
    required this.programFontSize,
  });

  Map<String, dynamic> toJson() {
    return {
      'nameX': nameX,
      'nameY': nameY,
      'nameFontSize': nameFontSize,
      'idX': idX,
      'idY': idY,
      'idFontSize': idFontSize,
      'programX': programX,
      'programY': programY,
      'programFontSize': programFontSize,
    };
  }

  @override
  List<Object> get props => [
        nameX,
        nameY,
        nameFontSize,
        idX,
        idY,
        idFontSize,
        programX,
        programY,
        programFontSize,
      ];
}

/// ID Card status enumeration
enum IDCardStatus {
  active,
  expired,
  suspended,
  revoked;

  String get displayName {
    switch (this) {
      case IDCardStatus.active:
        return 'Active';
      case IDCardStatus.expired:
        return 'Expired';
      case IDCardStatus.suspended:
        return 'Suspended';
      case IDCardStatus.revoked:
        return 'Revoked';
    }
  }
}

/// ID Card design type enumeration
enum IDCardDesignType {
  modern,
  classic,
  minimal,
  corporate,
  creative;

  String get displayName {
    switch (this) {
      case IDCardDesignType.modern:
        return 'Modern';
      case IDCardDesignType.classic:
        return 'Classic';
      case IDCardDesignType.minimal:
        return 'Minimal';
      case IDCardDesignType.corporate:
        return 'Corporate';
      case IDCardDesignType.creative:
        return 'Creative';
    }
  }
}

/// Export format enumeration
enum ExportFormat {
  pdf,
  cdr,
  png,
  svg;

  /// Get display name for the format
  String get displayName {
    switch (this) {
      case ExportFormat.pdf:
        return 'PDF';
      case ExportFormat.cdr:
        return 'CDR (CorelDRAW)';
      case ExportFormat.png:
        return 'PNG Image';
      case ExportFormat.svg:
        return 'SVG Vector';
    }
  }

  String get fileExtension {
    switch (this) {
      case ExportFormat.pdf:
        return '.pdf';
      case ExportFormat.cdr:
        return '.cdr';
      case ExportFormat.png:
        return '.png';
      case ExportFormat.svg:
        return '.svg';
    }
  }
}
