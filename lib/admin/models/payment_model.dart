import 'package:cloud_firestore/cloud_firestore.dart';

/// Payment model for managing student payments and receipts
/// 
/// This model handles:
/// - Tuition fee payments and tracking
/// - Course material payments
/// - Payment receipt generation
/// - Payment history and analytics
class Payment {
  /// Unique identifier for the payment
  final String id;
  
  /// Student ID who made the payment
  final String studentId;
  
  /// Student name for display
  final String studentName;
  
  /// Student email for receipts
  final String studentEmail;
  
  /// Payment amount
  final double amount;
  
  /// Payment currency (USD, EUR, etc.)
  final String currency;
  
  /// Payment type (tuition, materials, etc.)
  final PaymentType type;
  
  /// Payment method (cash, card, bank transfer, etc.)
  final PaymentMethod method;
  
  /// Payment status
  final PaymentStatus status;
  
  /// Payment description/purpose
  final String description;
  
  /// Course ID (if payment is for a specific course)
  final String? courseId;
  
  /// Course name for display
  final String? courseName;
  
  /// Academic term/semester
  final String? academicTerm;
  
  /// Academic year
  final String? academicYear;
  
  /// Payment due date
  final DateTime? dueDate;
  
  /// Payment date (when payment was made)
  final DateTime? paidAt;
  
  /// Payment creation date
  final DateTime createdAt;
  
  /// Last update timestamp
  final DateTime? updatedAt;
  
  /// Transaction ID from payment processor
  final String? transactionId;
  
  /// Receipt number
  final String receiptNumber;
  
  /// Payment processor reference
  final String? processorReference;
  
  /// Late fee amount (if applicable)
  final double? lateFee;
  
  /// Discount amount applied
  final double? discountAmount;
  
  /// Discount reason/code
  final String? discountReason;
  
  /// Tax amount
  final double? taxAmount;
  
  /// Total amount after discounts and fees
  final double totalAmount;
  
  /// Payment notes/comments
  final String? notes;
  
  /// Who processed/recorded the payment
  final String? processedBy;
  
  /// Payment installment information
  final PaymentInstallment? installment;
  
  /// Whether payment is refundable
  final bool isRefundable;
  
  /// Refund information (if refunded)
  final PaymentRefund? refund;

  const Payment({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.studentEmail,
    required this.amount,
    this.currency = 'USD',
    required this.type,
    required this.method,
    this.status = PaymentStatus.pending,
    required this.description,
    this.courseId,
    this.courseName,
    this.academicTerm,
    this.academicYear,
    this.dueDate,
    this.paidAt,
    required this.createdAt,
    this.updatedAt,
    this.transactionId,
    required this.receiptNumber,
    this.processorReference,
    this.lateFee,
    this.discountAmount,
    this.discountReason,
    this.taxAmount,
    required this.totalAmount,
    this.notes,
    this.processedBy,
    this.installment,
    this.isRefundable = true,
    this.refund,
  });

  /// Factory constructor to create Payment from Firebase JSON data
  factory Payment.fromJson(Map<String, dynamic> json, String id) {
    try {
      return Payment(
        id: id,
        studentId: json['studentId']?.toString() ?? '',
        studentName: json['studentName']?.toString() ?? '',
        studentEmail: json['studentEmail']?.toString() ?? '',
        amount: _parseDouble(json['amount']),
        currency: json['currency']?.toString() ?? 'USD',
        type: PaymentType.values.firstWhere(
          (type) => type.name == json['type'],
          orElse: () => PaymentType.tuition,
        ),
        method: PaymentMethod.values.firstWhere(
          (method) => method.name == json['method'],
          orElse: () => PaymentMethod.cash,
        ),
        status: PaymentStatus.values.firstWhere(
          (status) => status.name == json['status'],
          orElse: () => PaymentStatus.pending,
        ),
        description: json['description']?.toString() ?? '',
        courseId: json['courseId']?.toString(),
        courseName: json['courseName']?.toString(),
        academicTerm: json['academicTerm']?.toString(),
        academicYear: json['academicYear']?.toString(),
        dueDate: json['dueDate'] != null ? _parseTimestamp(json['dueDate']) : null,
        paidAt: json['paidAt'] != null ? _parseTimestamp(json['paidAt']) : null,
        createdAt: _parseTimestamp(json['createdAt']),
        updatedAt: json['updatedAt'] != null ? _parseTimestamp(json['updatedAt']) : null,
        transactionId: json['transactionId']?.toString(),
        receiptNumber: json['receiptNumber']?.toString() ?? '',
        processorReference: json['processorReference']?.toString(),
        lateFee: json['lateFee'] != null ? _parseDouble(json['lateFee']) : null,
        discountAmount: json['discountAmount'] != null ? _parseDouble(json['discountAmount']) : null,
        discountReason: json['discountReason']?.toString(),
        taxAmount: json['taxAmount'] != null ? _parseDouble(json['taxAmount']) : null,
        totalAmount: _parseDouble(json['totalAmount']),
        notes: json['notes']?.toString(),
        processedBy: json['processedBy']?.toString(),
        installment: json['installment'] != null 
            ? PaymentInstallment.fromJson(json['installment']) 
            : null,
        isRefundable: json['isRefundable'] ?? true,
        refund: json['refund'] != null 
            ? PaymentRefund.fromJson(json['refund']) 
            : null,
      );
    } catch (e) {
      return Payment(
        id: id,
        studentId: '',
        studentName: 'Unknown Student',
        studentEmail: '<EMAIL>',
        amount: 0.0,
        type: PaymentType.tuition,
        method: PaymentMethod.cash,
        description: 'Payment data could not be loaded',
        receiptNumber: 'UNKNOWN',
        totalAmount: 0.0,
        createdAt: DateTime.now(),
      );
    }
  }

  /// Convert Payment instance to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'studentEmail': studentEmail,
      'amount': amount,
      'currency': currency,
      'type': type.name,
      'method': method.name,
      'status': status.name,
      'description': description,
      'courseId': courseId,
      'courseName': courseName,
      'academicTerm': academicTerm,
      'academicYear': academicYear,
      'dueDate': dueDate != null ? Timestamp.fromDate(dueDate!) : null,
      'paidAt': paidAt != null ? Timestamp.fromDate(paidAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'transactionId': transactionId,
      'receiptNumber': receiptNumber,
      'processorReference': processorReference,
      'lateFee': lateFee,
      'discountAmount': discountAmount,
      'discountReason': discountReason,
      'taxAmount': taxAmount,
      'totalAmount': totalAmount,
      'notes': notes,
      'processedBy': processedBy,
      'installment': installment?.toJson(),
      'isRefundable': isRefundable,
      'refund': refund?.toJson(),
    };
  }

  /// Create a copy of the payment with updated fields
  Payment copyWith({
    String? id,
    String? studentId,
    String? studentName,
    String? studentEmail,
    double? amount,
    String? currency,
    PaymentType? type,
    PaymentMethod? method,
    PaymentStatus? status,
    String? description,
    String? courseId,
    String? courseName,
    String? academicTerm,
    String? academicYear,
    DateTime? dueDate,
    DateTime? paidAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? transactionId,
    String? receiptNumber,
    String? processorReference,
    double? lateFee,
    double? discountAmount,
    String? discountReason,
    double? taxAmount,
    double? totalAmount,
    String? notes,
    String? processedBy,
    PaymentInstallment? installment,
    bool? isRefundable,
    PaymentRefund? refund,
  }) {
    return Payment(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      studentEmail: studentEmail ?? this.studentEmail,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      type: type ?? this.type,
      method: method ?? this.method,
      status: status ?? this.status,
      description: description ?? this.description,
      courseId: courseId ?? this.courseId,
      courseName: courseName ?? this.courseName,
      academicTerm: academicTerm ?? this.academicTerm,
      academicYear: academicYear ?? this.academicYear,
      dueDate: dueDate ?? this.dueDate,
      paidAt: paidAt ?? this.paidAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      transactionId: transactionId ?? this.transactionId,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      processorReference: processorReference ?? this.processorReference,
      lateFee: lateFee ?? this.lateFee,
      discountAmount: discountAmount ?? this.discountAmount,
      discountReason: discountReason ?? this.discountReason,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      notes: notes ?? this.notes,
      processedBy: processedBy ?? this.processedBy,
      installment: installment ?? this.installment,
      isRefundable: isRefundable ?? this.isRefundable,
      refund: refund ?? this.refund,
    );
  }

  /// Helper methods for safe parsing
  static DateTime _parseTimestamp(dynamic value) {
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
    return DateTime.now();
  }

  static double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  @override
  String toString() => 'Payment(id: $id, student: $studentName, amount: $totalAmount $currency)';
}

/// Payment installment information
class PaymentInstallment {
  final int installmentNumber;
  final int totalInstallments;
  final double installmentAmount;
  final DateTime nextDueDate;

  const PaymentInstallment({
    required this.installmentNumber,
    required this.totalInstallments,
    required this.installmentAmount,
    required this.nextDueDate,
  });

  factory PaymentInstallment.fromJson(Map<String, dynamic> json) {
    return PaymentInstallment(
      installmentNumber: json['installmentNumber'] ?? 1,
      totalInstallments: json['totalInstallments'] ?? 1,
      installmentAmount: json['installmentAmount']?.toDouble() ?? 0.0,
      nextDueDate: json['nextDueDate'] is Timestamp 
          ? (json['nextDueDate'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'installmentNumber': installmentNumber,
      'totalInstallments': totalInstallments,
      'installmentAmount': installmentAmount,
      'nextDueDate': Timestamp.fromDate(nextDueDate),
    };
  }
}

/// Payment refund information
class PaymentRefund {
  final String refundId;
  final double refundAmount;
  final String refundReason;
  final DateTime refundDate;
  final String refundedBy;

  const PaymentRefund({
    required this.refundId,
    required this.refundAmount,
    required this.refundReason,
    required this.refundDate,
    required this.refundedBy,
  });

  factory PaymentRefund.fromJson(Map<String, dynamic> json) {
    return PaymentRefund(
      refundId: json['refundId']?.toString() ?? '',
      refundAmount: json['refundAmount']?.toDouble() ?? 0.0,
      refundReason: json['refundReason']?.toString() ?? '',
      refundDate: json['refundDate'] is Timestamp 
          ? (json['refundDate'] as Timestamp).toDate()
          : DateTime.now(),
      refundedBy: json['refundedBy']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'refundId': refundId,
      'refundAmount': refundAmount,
      'refundReason': refundReason,
      'refundDate': Timestamp.fromDate(refundDate),
      'refundedBy': refundedBy,
    };
  }
}

/// Payment type enumeration
enum PaymentType {
  tuition,
  courseMaterials,
  examFee,
  labFee,
  libraryFee,
  applicationFee,
  lateFee,
  other;

  String get displayName {
    switch (this) {
      case PaymentType.tuition:
        return 'Tuition Fee';
      case PaymentType.courseMaterials:
        return 'Course Materials';
      case PaymentType.examFee:
        return 'Exam Fee';
      case PaymentType.labFee:
        return 'Lab Fee';
      case PaymentType.libraryFee:
        return 'Library Fee';
      case PaymentType.applicationFee:
        return 'Application Fee';
      case PaymentType.lateFee:
        return 'Late Fee';
      case PaymentType.other:
        return 'Other';
    }
  }
}

/// Payment method enumeration
enum PaymentMethod {
  cash,
  creditCard,
  debitCard,
  bankTransfer,
  check,
  onlinePayment,
  mobilePayment,
  other;

  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.onlinePayment:
        return 'Online Payment';
      case PaymentMethod.mobilePayment:
        return 'Mobile Payment';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}

/// Payment status enumeration
enum PaymentStatus {
  pending,
  paid,
  completed,
  overdue,
  cancelled,
  refunded,
  partiallyPaid;

  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.overdue:
        return 'Overdue';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partiallyPaid:
        return 'Partially Paid';
    }
  }
}
