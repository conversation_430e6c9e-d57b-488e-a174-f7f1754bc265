import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Exam model for managing institutional examinations
/// 
/// This model handles:
/// - Exam scheduling and management
/// - Multiple question types and formats
/// - Automated grading and result processing
/// - Exam analytics and reporting
/// - Integration with course and student systems
class Exam extends Equatable {
  /// Unique identifier for the exam
  final String id;
  
  /// Exam title/name
  final String title;
  
  /// Exam description
  final String description;
  
  /// Course ID this exam belongs to
  final String courseId;
  
  /// Course name for display
  final String courseName;
  
  /// Instructor/creator ID
  final String instructorId;
  
  /// Instructor name
  final String instructorName;
  
  /// Exam type (midterm, final, quiz, etc.)
  final ExamType type;
  
  /// Academic year
  final String academicYear;
  
  /// Academic term
  final String? academicTerm;
  
  /// Grade level this exam is for
  final String gradeLevel;
  
  /// Exam start date and time
  final DateTime startDateTime;
  
  /// Exam end date and time
  final DateTime endDateTime;
  
  /// Exam duration in minutes
  final int durationMinutes;
  
  /// Total marks/points for the exam
  final double totalMarks;
  
  /// Passing marks required
  final double passingMarks;
  
  /// List of exam questions
  final List<ExamQuestion> questions;
  
  /// Assigned students (if specific students)
  final List<String> assignedStudents;
  
  /// Whether exam is published/visible to students
  final bool isPublished;
  
  /// Whether exam results are published
  final bool resultsPublished;
  
  /// Exam instructions for students
  final String? instructions;
  
  /// Exam venue/location
  final String? venue;
  
  /// Maximum attempts allowed
  final int maxAttempts;
  
  /// Whether questions should be shuffled
  final bool shuffleQuestions;
  
  /// Whether answers should be shuffled
  final bool shuffleAnswers;
  
  /// Whether to show results immediately
  final bool showResultsImmediately;
  
  /// Whether to show correct answers after completion
  final bool showCorrectAnswers;
  
  /// Exam status
  final ExamStatus status;
  
  /// Creation timestamp
  final DateTime createdAt;
  
  /// Last update timestamp
  final DateTime? updatedAt;
  
  /// Additional metadata
  final Map<String, dynamic>? metadata;

  const Exam({
    required this.id,
    required this.title,
    required this.description,
    required this.courseId,
    required this.courseName,
    required this.instructorId,
    required this.instructorName,
    required this.type,
    required this.academicYear,
    this.academicTerm,
    required this.gradeLevel,
    required this.startDateTime,
    required this.endDateTime,
    required this.durationMinutes,
    required this.totalMarks,
    required this.passingMarks,
    this.questions = const [],
    this.assignedStudents = const [],
    this.isPublished = false,
    this.resultsPublished = false,
    this.instructions,
    this.venue,
    this.maxAttempts = 1,
    this.shuffleQuestions = false,
    this.shuffleAnswers = false,
    this.showResultsImmediately = false,
    this.showCorrectAnswers = false,
    this.status = ExamStatus.draft,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Factory constructor to create Exam from Firebase JSON data
  factory Exam.fromJson(Map<String, dynamic> json, String id) {
    try {
      final questionsList = (json['questions'] as List<dynamic>? ?? [])
          .map((question) => ExamQuestion.fromJson(question as Map<String, dynamic>))
          .toList();

      final assignedStudentsList = (json['assignedStudents'] as List<dynamic>? ?? [])
          .map((student) => student.toString())
          .toList();

      return Exam(
        id: id,
        title: json['title']?.toString() ?? '',
        description: json['description']?.toString() ?? '',
        courseId: json['courseId']?.toString() ?? '',
        courseName: json['courseName']?.toString() ?? '',
        instructorId: json['instructorId']?.toString() ?? '',
        instructorName: json['instructorName']?.toString() ?? '',
        type: ExamType.values.firstWhere(
          (type) => type.name == json['type'],
          orElse: () => ExamType.quiz,
        ),
        academicYear: json['academicYear']?.toString() ?? '',
        academicTerm: json['academicTerm']?.toString(),
        gradeLevel: json['gradeLevel']?.toString() ?? '',
        startDateTime: _parseTimestamp(json['startDateTime']),
        endDateTime: _parseTimestamp(json['endDateTime']),
        durationMinutes: json['durationMinutes'] ?? 60,
        totalMarks: _parseDouble(json['totalMarks']),
        passingMarks: _parseDouble(json['passingMarks']),
        questions: questionsList,
        assignedStudents: assignedStudentsList,
        isPublished: json['isPublished'] ?? false,
        resultsPublished: json['resultsPublished'] ?? false,
        instructions: json['instructions']?.toString(),
        venue: json['venue']?.toString(),
        maxAttempts: json['maxAttempts'] ?? 1,
        shuffleQuestions: json['shuffleQuestions'] ?? false,
        shuffleAnswers: json['shuffleAnswers'] ?? false,
        showResultsImmediately: json['showResultsImmediately'] ?? false,
        showCorrectAnswers: json['showCorrectAnswers'] ?? false,
        status: ExamStatus.values.firstWhere(
          (status) => status.name == json['status'],
          orElse: () => ExamStatus.draft,
        ),
        createdAt: _parseTimestamp(json['createdAt']),
        updatedAt: json['updatedAt'] != null ? _parseTimestamp(json['updatedAt']) : null,
        metadata: json['metadata'] as Map<String, dynamic>?,
      );
    } catch (e) {
      throw Exception('Failed to parse Exam from JSON: $e');
    }
  }

  /// Convert Exam instance to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'courseId': courseId,
      'courseName': courseName,
      'instructorId': instructorId,
      'instructorName': instructorName,
      'type': type.name,
      'academicYear': academicYear,
      'academicTerm': academicTerm,
      'gradeLevel': gradeLevel,
      'startDateTime': Timestamp.fromDate(startDateTime),
      'endDateTime': Timestamp.fromDate(endDateTime),
      'durationMinutes': durationMinutes,
      'totalMarks': totalMarks,
      'passingMarks': passingMarks,
      'questions': questions.map((question) => question.toJson()).toList(),
      'assignedStudents': assignedStudents,
      'isPublished': isPublished,
      'resultsPublished': resultsPublished,
      'instructions': instructions,
      'venue': venue,
      'maxAttempts': maxAttempts,
      'shuffleQuestions': shuffleQuestions,
      'shuffleAnswers': shuffleAnswers,
      'showResultsImmediately': showResultsImmediately,
      'showCorrectAnswers': showCorrectAnswers,
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'metadata': metadata,
    };
  }

  /// Create a copy of the exam with updated fields
  Exam copyWith({
    String? id,
    String? title,
    String? description,
    String? courseId,
    String? courseName,
    String? instructorId,
    String? instructorName,
    ExamType? type,
    String? academicYear,
    String? academicTerm,
    String? gradeLevel,
    DateTime? startDateTime,
    DateTime? endDateTime,
    int? durationMinutes,
    double? totalMarks,
    double? passingMarks,
    List<ExamQuestion>? questions,
    List<String>? assignedStudents,
    bool? isPublished,
    bool? resultsPublished,
    String? instructions,
    String? venue,
    int? maxAttempts,
    bool? shuffleQuestions,
    bool? shuffleAnswers,
    bool? showResultsImmediately,
    bool? showCorrectAnswers,
    ExamStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Exam(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      courseId: courseId ?? this.courseId,
      courseName: courseName ?? this.courseName,
      instructorId: instructorId ?? this.instructorId,
      instructorName: instructorName ?? this.instructorName,
      type: type ?? this.type,
      academicYear: academicYear ?? this.academicYear,
      academicTerm: academicTerm ?? this.academicTerm,
      gradeLevel: gradeLevel ?? this.gradeLevel,
      startDateTime: startDateTime ?? this.startDateTime,
      endDateTime: endDateTime ?? this.endDateTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      totalMarks: totalMarks ?? this.totalMarks,
      passingMarks: passingMarks ?? this.passingMarks,
      questions: questions ?? this.questions,
      assignedStudents: assignedStudents ?? this.assignedStudents,
      isPublished: isPublished ?? this.isPublished,
      resultsPublished: resultsPublished ?? this.resultsPublished,
      instructions: instructions ?? this.instructions,
      venue: venue ?? this.venue,
      maxAttempts: maxAttempts ?? this.maxAttempts,
      shuffleQuestions: shuffleQuestions ?? this.shuffleQuestions,
      shuffleAnswers: shuffleAnswers ?? this.shuffleAnswers,
      showResultsImmediately: showResultsImmediately ?? this.showResultsImmediately,
      showCorrectAnswers: showCorrectAnswers ?? this.showCorrectAnswers,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
    );
  }

  /// Calculate total marks from all questions
  double calculateTotalMarks() {
    return questions.fold(0.0, (total, question) => total + question.marks);
  }

  /// Check if exam is currently active
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startDateTime) && now.isBefore(endDateTime) && isPublished;
  }

  /// Check if exam is upcoming
  bool get isUpcoming {
    final now = DateTime.now();
    return now.isBefore(startDateTime) && isPublished;
  }

  /// Check if exam is completed
  bool get isCompleted {
    final now = DateTime.now();
    return now.isAfter(endDateTime);
  }

  /// Helper method to parse double values
  static double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  /// Helper method to parse Firestore timestamps
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();
    if (timestamp is Timestamp) return timestamp.toDate();
    if (timestamp is DateTime) return timestamp;
    if (timestamp is String) return DateTime.tryParse(timestamp) ?? DateTime.now();
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        courseId,
        courseName,
        instructorId,
        instructorName,
        type,
        academicYear,
        academicTerm,
        gradeLevel,
        startDateTime,
        endDateTime,
        durationMinutes,
        totalMarks,
        passingMarks,
        questions,
        assignedStudents,
        isPublished,
        resultsPublished,
        instructions,
        venue,
        maxAttempts,
        shuffleQuestions,
        shuffleAnswers,
        showResultsImmediately,
        showCorrectAnswers,
        status,
        createdAt,
        updatedAt,
        metadata,
      ];

  @override
  String toString() => 'Exam(id: $id, title: $title, type: $type, totalMarks: $totalMarks)';
}

/// Exam question model
class ExamQuestion extends Equatable {
  final String id;
  final String question;
  final QuestionType type;
  final List<String> options;
  final List<int> correctAnswers; // Indices of correct answers
  final double marks;
  final String? explanation;
  final String? imageUrl;
  final bool isRequired;

  const ExamQuestion({
    required this.id,
    required this.question,
    required this.type,
    this.options = const [],
    this.correctAnswers = const [],
    this.marks = 1.0,
    this.explanation,
    this.imageUrl,
    this.isRequired = true,
  });

  factory ExamQuestion.fromJson(Map<String, dynamic> json) {
    return ExamQuestion(
      id: json['id']?.toString() ?? '',
      question: json['question']?.toString() ?? '',
      type: QuestionType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => QuestionType.multipleChoice,
      ),
      options: (json['options'] as List<dynamic>? ?? [])
          .map((option) => option.toString())
          .toList(),
      correctAnswers: (json['correctAnswers'] as List<dynamic>? ?? [])
          .map((answer) => answer as int)
          .toList(),
      marks: Exam._parseDouble(json['marks'], defaultValue: 1.0),
      explanation: json['explanation']?.toString(),
      imageUrl: json['imageUrl']?.toString(),
      isRequired: json['isRequired'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'type': type.name,
      'options': options,
      'correctAnswers': correctAnswers,
      'marks': marks,
      'explanation': explanation,
      'imageUrl': imageUrl,
      'isRequired': isRequired,
    };
  }

  @override
  List<Object?> get props => [
        id,
        question,
        type,
        options,
        correctAnswers,
        marks,
        explanation,
        imageUrl,
        isRequired,
      ];
}

/// Question type enumeration
enum QuestionType {
  multipleChoice,
  singleChoice,
  trueFalse,
  shortAnswer,
  essay,
  fillInTheBlank,
  matching,
}

/// Exam type enumeration
enum ExamType {
  quiz,
  midterm,
  finalExam,
  assignment,
  project,
  practical,
  oral,
  written,
}

/// Exam status enumeration
enum ExamStatus {
  draft,
  scheduled,
  active,
  completed,
  cancelled,
  postponed,
}
