import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Certificate model for managing digital certificates
/// 
/// This model handles:
/// - Course completion certificates
/// - Achievement certificates
/// - Custom certificate templates
/// - Digital verification and QR codes
/// - Integration with existing certificate system
class Certificate extends Equatable {
  /// Unique identifier for the certificate
  final String id;
  
  /// Certificate title/name
  final String title;
  
  /// Certificate description
  final String description;
  
  /// Student ID who earned the certificate
  final String studentId;
  
  /// Student name for display
  final String studentName;
  
  /// Student email
  final String studentEmail;
  
  /// Course ID (if course completion certificate)
  final String? courseId;
  
  /// Course name (if applicable)
  final String? courseName;
  
  /// Certificate type
  final CertificateType type;
  
  /// Certificate template used
  final CertificateTemplate template;
  
  /// Issue date
  final DateTime issueDate;
  
  /// Expiry date (if applicable)
  final DateTime? expiryDate;
  
  /// Certificate URL (from existing Course model integration)
  final String certificateUrl;
  
  /// QR code data for verification
  final String qrCodeData;
  
  /// Verification URL
  final String verificationUrl;
  
  /// Certificate number/ID for verification
  final String certificateNumber;
  
  /// Issuing authority/institution
  final String issuingAuthority;
  
  /// Authorized signatory name
  final String signatoryName;
  
  /// Signatory title/position
  final String signatoryTitle;
  
  /// Grade/score achieved (if applicable)
  final double? grade;
  
  /// Grade percentage
  final double? percentage;
  
  /// Credits earned (if applicable)
  final int? credits;
  
  /// Academic year
  final String? academicYear;
  
  /// Academic term
  final String? academicTerm;
  
  /// Certificate status
  final CertificateStatus status;
  
  /// Whether certificate is verified
  final bool isVerified;
  
  /// Verification date
  final DateTime? verifiedAt;
  
  /// Verified by (admin user ID)
  final String? verifiedBy;
  
  /// Creation timestamp
  final DateTime createdAt;
  
  /// Last update timestamp
  final DateTime? updatedAt;
  
  /// Created by (admin user ID)
  final String createdBy;
  
  /// Additional metadata
  final Map<String, dynamic>? metadata;
  
  /// Certificate notes
  final String? notes;

  const Certificate({
    required this.id,
    required this.title,
    required this.description,
    required this.studentId,
    required this.studentName,
    required this.studentEmail,
    this.courseId,
    this.courseName,
    required this.type,
    required this.template,
    required this.issueDate,
    this.expiryDate,
    required this.certificateUrl,
    required this.qrCodeData,
    required this.verificationUrl,
    required this.certificateNumber,
    required this.issuingAuthority,
    required this.signatoryName,
    required this.signatoryTitle,
    this.grade,
    this.percentage,
    this.credits,
    this.academicYear,
    this.academicTerm,
    this.status = CertificateStatus.active,
    this.isVerified = false,
    this.verifiedAt,
    this.verifiedBy,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.metadata,
    this.notes,
  });

  /// Factory constructor to create Certificate from Firebase JSON data
  factory Certificate.fromJson(Map<String, dynamic> json, String id) {
    try {
      return Certificate(
        id: id,
        title: json['title']?.toString() ?? '',
        description: json['description']?.toString() ?? '',
        studentId: json['studentId']?.toString() ?? '',
        studentName: json['studentName']?.toString() ?? '',
        studentEmail: json['studentEmail']?.toString() ?? '',
        courseId: json['courseId']?.toString(),
        courseName: json['courseName']?.toString(),
        type: CertificateType.values.firstWhere(
          (type) => type.name == json['type'],
          orElse: () => CertificateType.completion,
        ),
        template: CertificateTemplate.values.firstWhere(
          (template) => template.name == json['template'],
          orElse: () => CertificateTemplate.standard,
        ),
        issueDate: _parseTimestamp(json['issueDate']),
        expiryDate: json['expiryDate'] != null ? _parseTimestamp(json['expiryDate']) : null,
        certificateUrl: json['certificateUrl']?.toString() ?? '',
        qrCodeData: json['qrCodeData']?.toString() ?? '',
        verificationUrl: json['verificationUrl']?.toString() ?? '',
        certificateNumber: json['certificateNumber']?.toString() ?? '',
        issuingAuthority: json['issuingAuthority']?.toString() ?? '',
        signatoryName: json['signatoryName']?.toString() ?? '',
        signatoryTitle: json['signatoryTitle']?.toString() ?? '',
        grade: json['grade'] != null ? _parseDouble(json['grade']) : null,
        percentage: json['percentage'] != null ? _parseDouble(json['percentage']) : null,
        credits: json['credits'],
        academicYear: json['academicYear']?.toString(),
        academicTerm: json['academicTerm']?.toString(),
        status: CertificateStatus.values.firstWhere(
          (status) => status.name == json['status'],
          orElse: () => CertificateStatus.active,
        ),
        isVerified: json['isVerified'] ?? false,
        verifiedAt: json['verifiedAt'] != null ? _parseTimestamp(json['verifiedAt']) : null,
        verifiedBy: json['verifiedBy']?.toString(),
        createdAt: _parseTimestamp(json['createdAt']),
        updatedAt: json['updatedAt'] != null ? _parseTimestamp(json['updatedAt']) : null,
        createdBy: json['createdBy']?.toString() ?? '',
        metadata: json['metadata'] as Map<String, dynamic>?,
        notes: json['notes']?.toString(),
      );
    } catch (e) {
      throw Exception('Failed to parse Certificate from JSON: $e');
    }
  }

  /// Convert Certificate instance to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'studentId': studentId,
      'studentName': studentName,
      'studentEmail': studentEmail,
      'courseId': courseId,
      'courseName': courseName,
      'type': type.name,
      'template': template.name,
      'issueDate': Timestamp.fromDate(issueDate),
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'certificateUrl': certificateUrl,
      'qrCodeData': qrCodeData,
      'verificationUrl': verificationUrl,
      'certificateNumber': certificateNumber,
      'issuingAuthority': issuingAuthority,
      'signatoryName': signatoryName,
      'signatoryTitle': signatoryTitle,
      'grade': grade,
      'percentage': percentage,
      'credits': credits,
      'academicYear': academicYear,
      'academicTerm': academicTerm,
      'status': status.name,
      'isVerified': isVerified,
      'verifiedAt': verifiedAt != null ? Timestamp.fromDate(verifiedAt!) : null,
      'verifiedBy': verifiedBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
      'metadata': metadata,
      'notes': notes,
    };
  }

  /// Create a copy of the certificate with updated fields
  Certificate copyWith({
    String? id,
    String? title,
    String? description,
    String? studentId,
    String? studentName,
    String? studentEmail,
    String? courseId,
    String? courseName,
    CertificateType? type,
    CertificateTemplate? template,
    DateTime? issueDate,
    DateTime? expiryDate,
    String? certificateUrl,
    String? qrCodeData,
    String? verificationUrl,
    String? certificateNumber,
    String? issuingAuthority,
    String? signatoryName,
    String? signatoryTitle,
    double? grade,
    double? percentage,
    int? credits,
    String? academicYear,
    String? academicTerm,
    CertificateStatus? status,
    bool? isVerified,
    DateTime? verifiedAt,
    String? verifiedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? metadata,
    String? notes,
  }) {
    return Certificate(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      studentEmail: studentEmail ?? this.studentEmail,
      courseId: courseId ?? this.courseId,
      courseName: courseName ?? this.courseName,
      type: type ?? this.type,
      template: template ?? this.template,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      qrCodeData: qrCodeData ?? this.qrCodeData,
      verificationUrl: verificationUrl ?? this.verificationUrl,
      certificateNumber: certificateNumber ?? this.certificateNumber,
      issuingAuthority: issuingAuthority ?? this.issuingAuthority,
      signatoryName: signatoryName ?? this.signatoryName,
      signatoryTitle: signatoryTitle ?? this.signatoryTitle,
      grade: grade ?? this.grade,
      percentage: percentage ?? this.percentage,
      credits: credits ?? this.credits,
      academicYear: academicYear ?? this.academicYear,
      academicTerm: academicTerm ?? this.academicTerm,
      status: status ?? this.status,
      isVerified: isVerified ?? this.isVerified,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata,
      notes: notes ?? this.notes,
    );
  }

  /// Check if certificate is expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// Check if certificate is valid
  bool get isValid {
    return status == CertificateStatus.active && !isExpired;
  }

  /// Helper method to parse double values
  static double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? defaultValue;
    return defaultValue;
  }

  /// Helper method to parse Firestore timestamps
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();
    if (timestamp is Timestamp) return timestamp.toDate();
    if (timestamp is DateTime) return timestamp;
    if (timestamp is String) return DateTime.tryParse(timestamp) ?? DateTime.now();
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        studentId,
        studentName,
        studentEmail,
        courseId,
        courseName,
        type,
        template,
        issueDate,
        expiryDate,
        certificateUrl,
        qrCodeData,
        verificationUrl,
        certificateNumber,
        issuingAuthority,
        signatoryName,
        signatoryTitle,
        grade,
        percentage,
        credits,
        academicYear,
        academicTerm,
        status,
        isVerified,
        verifiedAt,
        verifiedBy,
        createdAt,
        updatedAt,
        createdBy,
        metadata,
        notes,
      ];

  @override
  String toString() => 'Certificate(id: $id, title: $title, studentName: $studentName, type: $type)';
}

/// Certificate type enumeration
enum CertificateType {
  completion,
  achievement,
  participation,
  excellence,
  merit,
  distinction,
  custom,
}

/// Certificate template enumeration
enum CertificateTemplate {
  standard,
  elegant,
  modern,
  classic,
  professional,
  academic,
  custom,
}

/// Certificate status enumeration
enum CertificateStatus {
  active,
  revoked,
  expired,
  pending,
  draft,
}
