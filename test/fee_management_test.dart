import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:passion_admin_app/admin/blocs/fee/fee_bloc.dart';
import 'package:passion_admin_app/admin/repositories/fee_repository.dart';
import 'package:passion_admin_app/admin/screens/fee_management_screen.dart';

void main() {
  group('Fee Management Tests', () {
    testWidgets('Fee Management Screen loads correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => FeeBloc(feeRepository: FeeRepository()),
            child: const FeeManagementScreen(),
          ),
        ),
      );

      // Verify that the screen loads
      expect(find.text('Fee Management'), findsOneWidget);
      expect(find.text('Add Fee Structure'), findsOneWidget);
    });

    testWidgets('Add Fee Structure button is present', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => FeeBloc(feeRepository: FeeRepository()),
            child: const FeeManagementScreen(),
          ),
        ),
      );

      // Find the floating action button
      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.text('Add Fee Structure'), findsOneWidget);
    });

    testWidgets('Filter buttons are present', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => FeeBloc(feeRepository: FeeRepository()),
            child: const FeeManagementScreen(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Check for filter buttons
      expect(find.text('All'), findsOneWidget);
      expect(find.text('Active'), findsOneWidget);
      expect(find.text('Academic Year'), findsOneWidget);
      expect(find.text('Grade Level'), findsOneWidget);
    });
  });
}
